{"__meta": {"id": "X4b10d6ddbc584b112b2fb4a63c342441", "datetime": "2025-07-31 14:35:53", "utime": 1753961753.508245, "method": "POST", "uri": "/livewire/message/project.task-board.list-view", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 8, "messages": [{"message": "[14:35:49] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753961749.745782, "xdebug_link": null, "collector": "log"}, {"message": "[14:35:49] LOG.warning: parse_str(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Services\\DashCrmService.php on line 90", "message_html": null, "is_string": false, "label": "warning", "time": 1753961749.82329, "xdebug_link": null, "collector": "log"}, {"message": "[14:35:51] LOG.debug: array (\n  'status' => 'success',\n  'message' => 'Tasks updated successfully.',\n  'data' => \n  array (\n    0 => \n    array (\n      'id' => 578,\n      'title' => 'MS4',\n      'status' => 'complete',\n    ),\n  ),\n)", "message_html": null, "is_string": false, "label": "debug", "time": 1753961751.292743, "xdebug_link": null, "collector": "log"}, {"message": "[14:35:51] LOG.info: MilestoneNotificationTrait: Found completed milestone {\"milestone_id\":578,\"milestone_title\":\"MS4\"}", "message_html": null, "is_string": false, "label": "info", "time": 1753961751.293134, "xdebug_link": null, "collector": "log"}, {"message": "[14:35:51] LOG.debug: MilestoneNotificationTrait: User IDs for notification {\"user_ids\":[65]}", "message_html": null, "is_string": false, "label": "debug", "time": 1753961751.293269, "xdebug_link": null, "collector": "log"}, {"message": "[14:35:51] LOG.info: MilestoneNotificationTrait: Sending notifications to users {\"milestone_data\":{\"milestone_name\":\"MS4\",\"project_name\":\"\",\"completion_date\":\"2025-07-31\"},\"user_count\":1}", "message_html": null, "is_string": false, "label": "info", "time": 1753961751.30154, "xdebug_link": null, "collector": "log"}, {"message": "[14:35:51] LOG.warning: Callables of the form [\"Swift_Message\", \"Swift_Mime_SimpleMessage::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\Message.php on line 46", "message_html": null, "is_string": false, "label": "warning", "time": 1753961751.343574, "xdebug_link": null, "collector": "log"}, {"message": "[14:35:51] LOG.info: MilestoneNotificationTrait: Email sent successfully {\"user_email\":\"<EMAIL>\",\"locale\":\"ar\",\"milestone\":\"MS4\"}", "message_html": null, "is_string": false, "label": "info", "time": 1753961751.983181, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753961749.383644, "end": 1753961753.508275, "duration": 4.124630928039551, "duration_str": "4.12s", "measures": [{"label": "Booting", "start": 1753961749.383644, "relative_start": 0, "end": 1753961749.729132, "relative_end": 1753961749.729132, "duration": 0.3454878330230713, "duration_str": "345ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753961749.729142, "relative_start": 0.34549784660339355, "end": 1753961753.508277, "relative_end": 1.9073486328125e-06, "duration": 3.77913498878479, "duration_str": "3.78s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 41267680, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 8, "templates": [{"name": "mail.milestone_notification (\\resources\\views\\mail\\milestone_notification.blade.php)", "param_count": 26, "params": ["milestoneData", "notificationType", "locale", "connection", "queue", "chainConnection", "chainQueue", "chainCatchCallbacks", "delay", "afterCommit", "middleware", "chained", "message", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.project.task-board.list-view (\\resources\\views\\livewire\\project\\task-board\\list-view.blade.php)", "param_count": 46, "params": ["errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.project.bug-report.modals.Task-Initialise-WorkOrder (\\resources\\views\\livewire\\project\\bug-report\\modals\\Task-Initialise-WorkOrder.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.show-untangible-task (\\resources\\views\\livewire\\project\\task-board\\modals\\show-untangible-task.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.edit-untangible-task (\\resources\\views\\livewire\\project\\task-board\\modals\\edit-untangible-task.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.edit-tangible-task (\\resources\\views\\livewire\\project\\task-board\\modals\\edit-tangible-task.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.Bulk-Actions.assignUserToTaskBulk (\\resources\\views\\livewire\\project\\task-board\\modals\\Bulk-Actions\\assignUserToTaskBulk.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}, {"name": "livewire.project.task-board.modals.Bulk-Actions.updateTaskBulk (\\resources\\views\\livewire\\project\\task-board\\modals\\Bulk-Actions\\updateTaskBulk.blade.php)", "param_count": 59, "params": ["__env", "app", "errors", "_instance", "records", "itemId", "priorities", "usersForAssign", "userAssigned", "milestonesList", "selectedmilestone", "prioritiesList", "selected<PERSON><PERSON><PERSON><PERSON>", "work_order_type", "viewType", "Bulk_assign_to", "selected_WorkOrder", "work_orders_list", "selected_work_order_data", "sortField", "sortByPriority", "sortDirection", "start_date", "assign_to", "end_date", "change_status", "users", "selectedTasks", "taskStages", "milestones", "id_task", "activeTab", "taskDetails", "taskEdit", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "__currentLoopData", "user", "loop", "__empty_1", "task", "color", "index", "taskData", "html", "componentId", "componentTag"], "type": "blade"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.0164, "accumulated_duration_str": "16.4ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00368, "duration_str": "3.68ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 22.439}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7368 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_dev_db2", "start_percent": 22.439, "width_percent": 3.537}, {"sql": "select * from `crm_user` where `crm_user_id` in (65)", "type": "query", "params": [], "bindings": ["65"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 62}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 29}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Project\\TaskBoard\\ListView.php", "line": 143}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00176, "duration_str": "1.76ms", "stmt_id": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php:62", "connection": "osool_dev_db2", "start_percent": 25.976, "width_percent": 10.732}, {"sql": "insert into `notifications` (`user_id`, `message`, `message_ar`, `section_type`, `section_id`, `is_read`, `created_at`) values (7368, 'تم إكمال «MS4».', 'تم إكمال «MS4».', 'milestones', 635, 0, '2025-07-31 14:35:51')", "type": "query", "params": [], "bindings": ["7368", "تم إكمال &laquo;MS4&raquo;.", "تم إكمال &laquo;MS4&raquo;.", "milestones", "635", "0", "2025-07-31 14:35:51"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 86}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 29}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Livewire\\Project\\TaskBoard\\ListView.php", "line": 143}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00821, "duration_str": "8.21ms", "stmt_id": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php:86", "connection": "osool_dev_db2", "start_percent": 36.707, "width_percent": 50.061}, {"sql": "select * from `users` where `users`.`id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 19, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 117}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 98}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php", "line": 29}, {"index": 22, "namespace": null, "name": "\\app\\Http\\Livewire\\Project\\TaskBoard\\ListView.php", "line": 143}, {"index": 23, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00131, "duration_str": "1.31ms", "stmt_id": "\\app\\Http\\Traits\\MilestoneNotificationTrait.php:117", "connection": "osool_dev_db2", "start_percent": 86.768, "width_percent": 7.988}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00086, "duration_str": "860μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_dev_db2", "start_percent": 94.756, "width_percent": 5.244}]}, "models": {"data": {"App\\Models\\ProjectsDetails": 1, "App\\Models\\CrmUser": 1, "App\\Models\\UserCompany": 1, "App\\Models\\User": 2}, "count": 5}, "livewire": {"data": {"project.task-board.list-view #IWBGIJILwv0Kb6gFj0DH": "array:5 [\n  \"data\" => array:31 [\n    \"records\" => array:9 [\n      \"items\" => array:10 [\n        0 => array:21 [\n          \"id\" => 1609\n          \"title\" => \"TSK4\"\n          \"priority\" => \"High\"\n          \"description\" => \"b\"\n          \"start_date\" => \"2025-07-15 11:45:58\"\n          \"due_date\" => \"2025-07-15 11:45:58\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"<PERSON><PERSON><PERSON>\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-15T11:45:58.000000Z\"\n          \"updated_at\" => \"2025-07-15T11:48:42.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        1 => array:21 [\n          \"id\" => 1611\n          \"title\" => \"TASK5\"\n          \"priority\" => \"High\"\n          \"description\" => \"ASD\"\n          \"start_date\" => \"2025-07-15 11:54:16\"\n          \"due_date\" => \"2025-07-15 11:54:16\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-15T11:54:16.000000Z\"\n          \"updated_at\" => \"2025-07-15T11:55:07.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        2 => array:21 [\n          \"id\" => 1715\n          \"title\" => \"TTRREEE\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESC\"\n          \"start_date\" => \"2025-07-31 09:10:02\"\n          \"due_date\" => \"2025-07-31 09:10:02\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-31T09:10:02.000000Z\"\n          \"updated_at\" => \"2025-07-31T09:10:44.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        3 => array:21 [\n          \"id\" => 1716\n          \"title\" => \"TESTG\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESC\"\n          \"start_date\" => \"2025-07-31 09:24:31\"\n          \"due_date\" => \"2025-07-31 09:24:31\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-31T09:24:31.000000Z\"\n          \"updated_at\" => \"2025-07-31T09:25:06.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        4 => array:21 [\n          \"id\" => 1717\n          \"title\" => \"TESTG123\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESC\"\n          \"start_date\" => \"2025-07-31 10:03:00\"\n          \"due_date\" => \"2025-07-31 10:03:00\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-31T10:03:00.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:03:52.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        5 => array:21 [\n          \"id\" => 1718\n          \"title\" => \"TEST345\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESC\"\n          \"start_date\" => \"2025-07-31 10:07:41\"\n          \"due_date\" => \"2025-07-31 10:07:41\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-31T10:07:41.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:08:20.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        6 => array:21 [\n          \"id\" => 1719\n          \"title\" => \"TEST345123\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESC\"\n          \"start_date\" => \"2025-07-31 10:14:05\"\n          \"due_date\" => \"2025-07-31 10:14:05\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-31T10:14:05.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:20:01.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        7 => array:21 [\n          \"id\" => 1720\n          \"title\" => \"NDDDFFFF\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESC\"\n          \"start_date\" => \"2025-07-31 10:38:31\"\n          \"due_date\" => \"2025-07-31 10:38:31\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS5\"\n          \"milestone_id\" => 579\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-31T10:38:31.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:42:16.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        8 => array:21 [\n          \"id\" => 1723\n          \"title\" => \"123\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESC\"\n          \"start_date\" => \"2025-07-31 10:58:30\"\n          \"due_date\" => \"2025-07-31 10:58:30\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS4\"\n          \"milestone_id\" => 578\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-31T10:58:30.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:58:42.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n        9 => array:21 [\n          \"id\" => 1724\n          \"title\" => \"123\"\n          \"priority\" => \"High\"\n          \"description\" => \"DESC\"\n          \"start_date\" => \"2025-07-31 11:31:57\"\n          \"due_date\" => \"2025-07-31 11:31:57\"\n          \"task_type\" => \"untangible\"\n          \"workorder_id\" => null\n          \"workorder_type\" => null\n          \"property_name\" => null\n          \"order\" => 0\n          \"assign_to\" => array:1 [\n            0 => array:3 [\n              \"id\" => 65\n              \"name\" => \"Khansaa Hasan\"\n              \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n            ]\n          ]\n          \"project_name\" => \"dup 1\"\n          \"milestone_name\" => \"MS4\"\n          \"milestone_id\" => 578\n          \"stage_name\" => \"Done\"\n          \"created_at\" => \"2025-07-31T11:31:57.000000Z\"\n          \"updated_at\" => \"2025-07-31T11:35:51.000000Z\"\n          \"files\" => []\n          \"comments\" => []\n          \"subTasks\" => []\n        ]\n      ]\n      \"next_page_url\" => null\n      \"prev_page_url\" => null\n      \"per_page\" => 10\n      \"total\" => 10\n      \"allUsers\" => array:1 [\n        0 => array:3 [\n          \"id\" => 65\n          \"name\" => \"Khansaa Hasan\"\n          \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n        ]\n      ]\n      \"allMilestones\" => array:5 [\n        0 => array:2 [\n          \"id\" => 562\n          \"title\" => \"TEST\"\n        ]\n        1 => array:2 [\n          \"id\" => 563\n          \"title\" => \"MILESTONE2\"\n        ]\n        2 => array:2 [\n          \"id\" => 577\n          \"title\" => \"MILESTONE3\"\n        ]\n        3 => array:2 [\n          \"id\" => 578\n          \"title\" => \"MS4\"\n        ]\n        4 => array:2 [\n          \"id\" => 579\n          \"title\" => \"MS5\"\n        ]\n      ]\n      \"priority\" => array:3 [\n        \"Low\" => \"Low\"\n        \"Medium\" => \"Medium\"\n        \"High\" => \"High\"\n      ]\n      \"taskStages\" => array:5 [\n        0 => array:9 [\n          \"id\" => 462\n          \"name\" => \"Tangible WO\"\n          \"color\" => \"#000000\"\n          \"complete\" => 0\n          \"workspace_id\" => 46\n          \"order\" => 0\n          \"created_by\" => 65\n          \"created_at\" => \"2025-05-28T11:56:32.000000Z\"\n          \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n        ]\n        1 => array:9 [\n          \"id\" => 101\n          \"name\" => \"Todo\"\n          \"color\" => \"#77b6ea\"\n          \"complete\" => 0\n          \"workspace_id\" => 46\n          \"order\" => 1\n          \"created_by\" => 65\n          \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n          \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n        ]\n        2 => array:9 [\n          \"id\" => 102\n          \"name\" => \"In Progress\"\n          \"color\" => \"#545454\"\n          \"complete\" => 0\n          \"workspace_id\" => 46\n          \"order\" => 2\n          \"created_by\" => 65\n          \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n          \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n        ]\n        3 => array:9 [\n          \"id\" => 103\n          \"name\" => \"Review\"\n          \"color\" => \"#3cb8d9\"\n          \"complete\" => 0\n          \"workspace_id\" => 46\n          \"order\" => 3\n          \"created_by\" => 65\n          \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n          \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n        ]\n        4 => array:9 [\n          \"id\" => 104\n          \"name\" => \"Done\"\n          \"color\" => \"#37b37e\"\n          \"complete\" => 1\n          \"workspace_id\" => 46\n          \"order\" => 4\n          \"created_by\" => 65\n          \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n          \"updated_at\" => \"2025-07-13T15:33:32.000000Z\"\n        ]\n      ]\n    ]\n    \"itemId\" => 635\n    \"priorities\" => array:3 [\n      \"Low\" => \"Low\"\n      \"Medium\" => \"Medium\"\n      \"High\" => \"High\"\n    ]\n    \"usersForAssign\" => array:1 [\n      0 => array:3 [\n        \"id\" => 65\n        \"name\" => \"Khansaa Hasan\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"userAssigned\" => \"\"\n    \"milestonesList\" => array:5 [\n      0 => array:2 [\n        \"id\" => 562\n        \"title\" => \"TEST\"\n      ]\n      1 => array:2 [\n        \"id\" => 563\n        \"title\" => \"MILESTONE2\"\n      ]\n      2 => array:2 [\n        \"id\" => 577\n        \"title\" => \"MILESTONE3\"\n      ]\n      3 => array:2 [\n        \"id\" => 578\n        \"title\" => \"MS4\"\n      ]\n      4 => array:2 [\n        \"id\" => 579\n        \"title\" => \"MS5\"\n      ]\n    ]\n    \"selectedmilestone\" => \"\"\n    \"prioritiesList\" => array:3 [\n      \"Low\" => \"Low\"\n      \"Medium\" => \"Medium\"\n      \"High\" => \"High\"\n    ]\n    \"selectedprioritie\" => \"\"\n    \"work_order_type\" => null\n    \"viewType\" => null\n    \"Bulk_assign_to\" => null\n    \"selected_WorkOrder\" => \"\"\n    \"work_orders_list\" => []\n    \"selected_work_order_data\" => null\n    \"sortField\" => \"\"\n    \"sortByPriority\" => \"\"\n    \"sortDirection\" => \"asc\"\n    \"start_date\" => \"\"\n    \"assign_to\" => \"\"\n    \"end_date\" => \"\"\n    \"change_status\" => \"Done\"\n    \"users\" => array:1 [\n      0 => array:3 [\n        \"id\" => 65\n        \"name\" => \"Khansaa Hasan\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"selectedTasks\" => []\n    \"taskStages\" => array:5 [\n      0 => array:9 [\n        \"id\" => 462\n        \"name\" => \"Tangible WO\"\n        \"color\" => \"#000000\"\n        \"complete\" => 0\n        \"workspace_id\" => 46\n        \"order\" => 0\n        \"created_by\" => 65\n        \"created_at\" => \"2025-05-28T11:56:32.000000Z\"\n        \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n      ]\n      1 => array:9 [\n        \"id\" => 101\n        \"name\" => \"Todo\"\n        \"color\" => \"#77b6ea\"\n        \"complete\" => 0\n        \"workspace_id\" => 46\n        \"order\" => 1\n        \"created_by\" => 65\n        \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n        \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n      ]\n      2 => array:9 [\n        \"id\" => 102\n        \"name\" => \"In Progress\"\n        \"color\" => \"#545454\"\n        \"complete\" => 0\n        \"workspace_id\" => 46\n        \"order\" => 2\n        \"created_by\" => 65\n        \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n        \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n      ]\n      3 => array:9 [\n        \"id\" => 103\n        \"name\" => \"Review\"\n        \"color\" => \"#3cb8d9\"\n        \"complete\" => 0\n        \"workspace_id\" => 46\n        \"order\" => 3\n        \"created_by\" => 65\n        \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n        \"updated_at\" => \"2025-05-28T11:56:32.000000Z\"\n      ]\n      4 => array:9 [\n        \"id\" => 104\n        \"name\" => \"Done\"\n        \"color\" => \"#37b37e\"\n        \"complete\" => 1\n        \"workspace_id\" => 46\n        \"order\" => 4\n        \"created_by\" => 65\n        \"created_at\" => \"2025-01-22T13:42:17.000000Z\"\n        \"updated_at\" => \"2025-07-13T15:33:32.000000Z\"\n      ]\n    ]\n    \"milestones\" => array:5 [\n      0 => array:2 [\n        \"id\" => 562\n        \"title\" => \"TEST\"\n      ]\n      1 => array:2 [\n        \"id\" => 563\n        \"title\" => \"MILESTONE2\"\n      ]\n      2 => array:2 [\n        \"id\" => 577\n        \"title\" => \"MILESTONE3\"\n      ]\n      3 => array:2 [\n        \"id\" => 578\n        \"title\" => \"MS4\"\n      ]\n      4 => array:2 [\n        \"id\" => 579\n        \"title\" => \"MS5\"\n      ]\n    ]\n    \"id_task\" => null\n    \"activeTab\" => \"comments\"\n    \"taskDetails\" => array:10 [\n      \"id_task\" => 0\n      \"title\" => \"\"\n      \"priority\" => \"\"\n      \"assign_to\" => []\n      \"milestone\" => \"\"\n      \"description\" => \"\"\n      \"start_date\" => \"\"\n      \"due_date\" => \"\"\n      \"comments\" => []\n      \"users\" => []\n    ]\n    \"taskEdit\" => array:13 [\n      \"title\" => \"\"\n      \"priority\" => \"\"\n      \"selected_priority\" => \"\"\n      \"assign_to\" => []\n      \"selected_assign_to\" => []\n      \"milestone\" => \"\"\n      \"selected_milestone\" => \"\"\n      \"description\" => \"\"\n      \"start_date\" => \"\"\n      \"due_date\" => \"\"\n      \"workorder_id\" => \"\"\n      \"workorder_type\" => \"\"\n      \"property_name\" => \"\"\n    ]\n    \"currentPage\" => array:1 [\n      \"fetchData\" => 1\n    ]\n  ]\n  \"name\" => \"project.task-board.list-view\"\n  \"view\" => \"livewire.project.task-board.list-view\"\n  \"component\" => \"App\\Http\\Livewire\\Project\\TaskBoard\\ListView\"\n  \"id\" => \"IWBGIJILwv0Kb6gFj0DH\"\n]"}, "count": 1}, "swiftmailer_mails": {"count": 1, "mails": [{"to": "<<EMAIL>>", "subject": "تم إنجاز المرحلة «Unknown Milestone»", "headers": "Message-ID: <<EMAIL>>\r\nDate: Thu, 31 Jul 2025 14:35:51 +0300\r\nSubject: =?utf-8?Q?=D8=AA=D9=85_=D8=A5=D9=86=D8=AC=D8=A7=D8=B2?=\r\n =?utf-8?Q?_=D8=A7=D9=84=D9=85=D8=B1=D8=AD=D9=84=D8=A9_=C2=ABUnknown_Mi?=\r\n =?utf-8?Q?lestone=C2=BB?=\r\nFrom: Osool Team <<EMAIL>>\r\nTo: <EMAIL>\r\nMIME-Version: 1.0\r\nContent-Type: text/html; charset=utf-8\r\nContent-Transfer-Encoding: quoted-printable\r\n", "body": null, "html": null}]}, "gate": {"count": 0, "messages": []}, "session": {"_token": "82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL", "captcha_answer": "32", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/_debugbar/open?id=X886a3b2013d31edc6c57bea8aa348118&op=get\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "ar", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/livewire/message/project.task-board.list-view", "status_code": "<pre class=sf-dump id=sf-dump-1451279965 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1451279965\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-1805544096 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1805544096\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-938274426 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">IWBGIJILwv0Kb6gFj0DH</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"28 characters\">project.task-board.list-view</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"233 characters\">CRMProjects/eyJpdiI6IkZKNlpmTFZEVGNFSlRNSTBwREFpOXc9PSIsInZhbHVlIjoiS2QyZGowVWE5bkxhampHUlRocC9tZz09IiwibWFjIjoiZmUxN2UwZjNhZWFmMTNkNTgyNDZlN2Q1Yzc4M2E1ZTNiYjVjOTZjODViYWNjNzJmZDY4ZmY5ZjM5NjU2YjQ3MyIsInRhZyI6IiJ9/task-board-list-view</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>l38340390-0</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">PDXVd23LQitD5Z18X3dV</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>comments-0</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">HRgCMDkEOY72ydY3JqIS</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>l38340390-1</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">WJuR9JqKwXiYQJsrtJQM</span>\"\n        \"<span class=sf-dump-key>tag</span>\" => \"<span class=sf-dump-str title=\"3 characters\">div</span>\"\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">40602a95</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:31</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>records</span>\" => <span class=sf-dump-note>array:9</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>items</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1609</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">TSK4</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str>b</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-15 11:45:58</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-15 11:45:58</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-15T11:45:58.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-15T11:48:42.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1611</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">TASK5</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"3 characters\">ASD</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-15 11:54:16</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-15 11:54:16</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-15T11:54:16.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-15T11:55:07.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1715</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">TTRREEE</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">DESC</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 09:10:02</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 09:10:02</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T09:10:02.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T09:10:44.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1716</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"5 characters\">TESTG</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">DESC</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 09:24:31</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 09:24:31</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T09:24:31.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T09:25:06.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1717</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">TESTG123</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">DESC</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 10:03:00</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 10:03:00</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T10:03:00.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T10:03:52.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1718</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"7 characters\">TEST345</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">DESC</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 10:07:41</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 10:07:41</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T10:07:41.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T10:08:20.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1719</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">TEST345123</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">DESC</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 10:14:05</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 10:14:05</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T10:14:05.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T10:20:01.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1720</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"8 characters\">NDDDFFFF</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">DESC</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 10:38:31</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 10:38:31</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T10:38:31.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T10:42:16.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1723</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">123</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">DESC</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 10:58:30</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 10:58:30</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS4</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>578</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T10:58:30.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T10:58:42.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n          <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:21</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1724</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">123</span>\"\n            \"<span class=sf-dump-key>priority</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n            \"<span class=sf-dump-key>description</span>\" => \"<span class=sf-dump-str title=\"4 characters\">DESC</span>\"\n            \"<span class=sf-dump-key>start_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 11:31:57</span>\"\n            \"<span class=sf-dump-key>due_date</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-07-31 11:31:57</span>\"\n            \"<span class=sf-dump-key>task_type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">untangible</span>\"\n            \"<span class=sf-dump-key>workorder_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>workorder_type</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>property_name</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>assign_to</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=7 class=sf-dump-compact>\n              <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n                \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n                \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n              </samp>]\n            </samp>]\n            \"<span class=sf-dump-key>project_name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">dup 1</span>\"\n            \"<span class=sf-dump-key>milestone_name</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS4</span>\"\n            \"<span class=sf-dump-key>milestone_id</span>\" => <span class=sf-dump-num>578</span>\n            \"<span class=sf-dump-key>stage_name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tangible WO</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T11:31:57.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-31T11:31:57.000000Z</span>\"\n            \"<span class=sf-dump-key>files</span>\" => []\n            \"<span class=sf-dump-key>comments</span>\" => []\n            \"<span class=sf-dump-key>subTasks</span>\" => []\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>next_page_url</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>prev_page_url</span>\" => <span class=sf-dump-const>null</span>\n        \"<span class=sf-dump-key>per_page</span>\" => <span class=sf-dump-num>10</span>\n        \"<span class=sf-dump-key>total</span>\" => <span class=sf-dump-num>10</span>\n        \"<span class=sf-dump-key>allUsers</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n            \"<span class=sf-dump-key>avatar</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>allMilestones</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>562</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">TEST</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>563</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE2</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>577</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE3</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>578</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS4</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>579</span>\n            \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n          </samp>]\n        </samp>]\n        \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>Low</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Low</span>\"\n          \"<span class=sf-dump-key>Medium</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n          \"<span class=sf-dump-key>High</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n        </samp>]\n        \"<span class=sf-dump-key>taskStages</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=5 class=sf-dump-compact>\n          <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>462</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tangible WO</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#000000</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n          </samp>]\n          <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>101</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Todo</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#77b6ea</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n          </samp>]\n          <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>102</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">In Progress</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#545454</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n          </samp>]\n          <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>103</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Review</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#3cb8d9</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n          </samp>]\n          <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>104</span>\n            \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n            \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#37b37e</span>\"\n            \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>1</span>\n            \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n            \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n            \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-13T15:33:32.000000Z</span>\"\n          </samp>]\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>itemId</span>\" => <span class=sf-dump-num>635</span>\n      \"<span class=sf-dump-key>priorities</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Low</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Low</span>\"\n        \"<span class=sf-dump-key>Medium</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n        \"<span class=sf-dump-key>High</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>usersForAssign</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>userAssigned</span>\" => \"\"\n      \"<span class=sf-dump-key>milestonesList</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>562</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">TEST</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>563</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE2</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>577</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE3</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>578</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS4</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>579</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>selectedmilestone</span>\" => \"\"\n      \"<span class=sf-dump-key>prioritiesList</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Low</span>\" => \"<span class=sf-dump-str title=\"3 characters\">Low</span>\"\n        \"<span class=sf-dump-key>Medium</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Medium</span>\"\n        \"<span class=sf-dump-key>High</span>\" => \"<span class=sf-dump-str title=\"4 characters\">High</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>selectedprioritie</span>\" => \"\"\n      \"<span class=sf-dump-key>work_order_type</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>viewType</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>Bulk_assign_to</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>selected_WorkOrder</span>\" => \"\"\n      \"<span class=sf-dump-key>work_orders_list</span>\" => []\n      \"<span class=sf-dump-key>selected_work_order_data</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>sortField</span>\" => \"\"\n      \"<span class=sf-dump-key>sortByPriority</span>\" => \"\"\n      \"<span class=sf-dump-key>sortDirection</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n      \"<span class=sf-dump-key>start_date</span>\" => \"\"\n      \"<span class=sf-dump-key>assign_to</span>\" => \"\"\n      \"<span class=sf-dump-key>end_date</span>\" => \"\"\n      \"<span class=sf-dump-key>change_status</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n      \"<span class=sf-dump-key>users</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:3</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"13 characters\">Khansaa Hasan</span>\"\n          \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>selectedTasks</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">1724</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>taskStages</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>462</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Tangible WO</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#000000</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>101</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Todo</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#77b6ea</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>102</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"11 characters\">In Progress</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#545454</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>2</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>103</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"6 characters\">Review</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#3cb8d9</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>0</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>3</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-05-28T11:56:32.000000Z</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:9</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>104</span>\n          \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"4 characters\">Done</span>\"\n          \"<span class=sf-dump-key>color</span>\" => \"<span class=sf-dump-str title=\"7 characters\">#37b37e</span>\"\n          \"<span class=sf-dump-key>complete</span>\" => <span class=sf-dump-num>1</span>\n          \"<span class=sf-dump-key>workspace_id</span>\" => <span class=sf-dump-num>46</span>\n          \"<span class=sf-dump-key>order</span>\" => <span class=sf-dump-num>4</span>\n          \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>65</span>\n          \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-01-22T13:42:17.000000Z</span>\"\n          \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"27 characters\">2025-07-13T15:33:32.000000Z</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>milestones</span>\" => <span class=sf-dump-note>array:5</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>562</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"4 characters\">TEST</span>\"\n        </samp>]\n        <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>563</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE2</span>\"\n        </samp>]\n        <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>577</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"10 characters\">MILESTONE3</span>\"\n        </samp>]\n        <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>578</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS4</span>\"\n        </samp>]\n        <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=5 class=sf-dump-compact>\n          \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>579</span>\n          \"<span class=sf-dump-key>title</span>\" => \"<span class=sf-dump-str title=\"3 characters\">MS5</span>\"\n        </samp>]\n      </samp>]\n      \"<span class=sf-dump-key>id_task</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>activeTab</span>\" => \"<span class=sf-dump-str title=\"8 characters\">comments</span>\"\n      \"<span class=sf-dump-key>taskDetails</span>\" => <span class=sf-dump-note>array:10</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id_task</span>\" => <span class=sf-dump-num>0</span>\n        \"<span class=sf-dump-key>title</span>\" => \"\"\n        \"<span class=sf-dump-key>priority</span>\" => \"\"\n        \"<span class=sf-dump-key>assign_to</span>\" => []\n        \"<span class=sf-dump-key>milestone</span>\" => \"\"\n        \"<span class=sf-dump-key>description</span>\" => \"\"\n        \"<span class=sf-dump-key>start_date</span>\" => \"\"\n        \"<span class=sf-dump-key>due_date</span>\" => \"\"\n        \"<span class=sf-dump-key>comments</span>\" => []\n        \"<span class=sf-dump-key>users</span>\" => []\n      </samp>]\n      \"<span class=sf-dump-key>taskEdit</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>title</span>\" => \"\"\n        \"<span class=sf-dump-key>priority</span>\" => \"\"\n        \"<span class=sf-dump-key>selected_priority</span>\" => \"\"\n        \"<span class=sf-dump-key>assign_to</span>\" => []\n        \"<span class=sf-dump-key>selected_assign_to</span>\" => []\n        \"<span class=sf-dump-key>milestone</span>\" => \"\"\n        \"<span class=sf-dump-key>selected_milestone</span>\" => \"\"\n        \"<span class=sf-dump-key>description</span>\" => \"\"\n        \"<span class=sf-dump-key>start_date</span>\" => \"\"\n        \"<span class=sf-dump-key>due_date</span>\" => \"\"\n        \"<span class=sf-dump-key>workorder_id</span>\" => \"\"\n        \"<span class=sf-dump-key>workorder_type</span>\" => \"\"\n        \"<span class=sf-dump-key>property_name</span>\" => \"\"\n      </samp>]\n      \"<span class=sf-dump-key>currentPage</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>fetchData</span>\" => <span class=sf-dump-num>1</span>\n      </samp>]\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">36ebc72b357d030513b1604c1e732752a3f95854dcf5b5cdee21ff4cc03304d9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">c2gr</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">updateSelectedStatus</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-938274426\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1090556478 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">10229</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"262 characters\">http://osool-b2g.test/CRMProjects/eyJpdiI6IkZKNlpmTFZEVGNFSlRNSTBwREFpOXc9PSIsInZhbHVlIjoiS2QyZGowVWE5bkxhampHUlRocC9tZz09IiwibWFjIjoiZmUxN2UwZjNhZWFmMTNkNTgyNDZlN2Q1Yzc4M2E1ZTNiYjVjOTZjODViYWNjNzJmZDY4ZmY5ZjM5NjU2YjQ3MyIsInRhZyI6IiJ9/task-board-list-view?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjJWZkZielQzMVQ5cTdNalorOUxIQ1E9PSIsInZhbHVlIjoiNkxqZUQ3bFBrOGdWK1VnSWJ1eVdHdVdGRktyZnpEYlB3ZDBOWHViQTdReFZKNE9LZlMzblRBbk5RNTBHeWdLaFFPQm5RV0dNL2tMY2RlUlpucnpjREpITW5nWXN3VlRCK1hhUDd4cUQ1dmR6WGdrV1hQTFMySk1LZERyR2VrWFYiLCJtYWMiOiI0NDdlN2E2MTEyYzIxYTNhMGY2NWE1YjU3YTZkZTFiZGJiMGMzZDdlODc3NmQ0Y2JlYzUzODNlMTlkMTU3Y2E4IiwidGFnIjoiIn0%3D; osool_session=ZGb5Z9CKCQ43NauWiKbO0avhMweFn67F710eIGDp</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1090556478\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1928172407 data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"5 characters\">10229</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"262 characters\">http://osool-b2g.test/CRMProjects/eyJpdiI6IkZKNlpmTFZEVGNFSlRNSTBwREFpOXc9PSIsInZhbHVlIjoiS2QyZGowVWE5bkxhampHUlRocC9tZz09IiwibWFjIjoiZmUxN2UwZjNhZWFmMTNkNTgyNDZlN2Q1Yzc4M2E1ZTNiYjVjOTZjODViYWNjNzJmZDY4ZmY5ZjM5NjU2YjQ3MyIsInRhZyI6IiJ9/task-board-list-view?page=1</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IjJWZkZielQzMVQ5cTdNalorOUxIQ1E9PSIsInZhbHVlIjoiNkxqZUQ3bFBrOGdWK1VnSWJ1eVdHdVdGRktyZnpEYlB3ZDBOWHViQTdReFZKNE9LZlMzblRBbk5RNTBHeWdLaFFPQm5RV0dNL2tMY2RlUlpucnpjREpITW5nWXN3VlRCK1hhUDd4cUQ1dmR6WGdrV1hQTFMySk1LZERyR2VrWFYiLCJtYWMiOiI0NDdlN2E2MTEyYzIxYTNhMGY2NWE1YjU3YTZkZTFiZGJiMGMzZDdlODc3NmQ0Y2JlYzUzODNlMTlkMTU3Y2E4IiwidGFnIjoiIn0%3D; osool_session=ZGb5Z9CKCQ43NauWiKbO0avhMweFn67F710eIGDp</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">62804</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"46 characters\">/livewire/message/project.task-board.list-view</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"46 characters\">/livewire/message/project.task-board.list-view</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753961749.3836</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753961749</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1928172407\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-849630145 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-849630145\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-11674329 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:35:53 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IlE1WDdwdG9TMzdmNklkUitEQi9yQkE9PSIsInZhbHVlIjoicisvT2ZUbzY1aHMzUGtTQUpucVdaSTBYVEhGRGFqeS8wUWl0bUVINjBXeVpqRWNJK1k5MDczaGJ4b1VOVmRyWk9yelo3czVIdWZNTzhpTFkwTjRwelEyVitOakVqTGlQa2RRNndOVkdEbXpUQllRd1Z6YzY4dm9MSlBvWXlWZ24iLCJtYWMiOiJiNWFlMjZhMzcxZDVhODNkNTkyM2E3NDVlNjEwMTY1Y2NmMGQ2NzY4MGRjNDE5NTFhMzUwMTA4ODZkMDcwOGU0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:35:53 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6IkRrekttd0RVOGw5aC9DSXJJWlVSNEE9PSIsInZhbHVlIjoiZmkwNkVJYk5rQnBxbTJlT0V2Y0lNTmd1S0p2eGpVeEdkdmdQL1V0c1NSM1BTaVpxbC9zWEVic2duL2VYNERxYVY1ajVPNGpEMnJ3c0NDS3dFMW5zay9MdDNQUkczMGNONlZoRHhYZkpuM2tMd0c4elFLekVwcDgwZG9qd0g5dkQiLCJtYWMiOiIzYWM1ZjA4Nzc2YjVlZmU5ODc5ZTE5ZWI0MmVhNjQzNmY5MTJjNzVmMmY1ODIyMmMyMmE1ZmExNzkzMTI3NDMxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:35:53 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IlE1WDdwdG9TMzdmNklkUitEQi9yQkE9PSIsInZhbHVlIjoicisvT2ZUbzY1aHMzUGtTQUpucVdaSTBYVEhGRGFqeS8wUWl0bUVINjBXeVpqRWNJK1k5MDczaGJ4b1VOVmRyWk9yelo3czVIdWZNTzhpTFkwTjRwelEyVitOakVqTGlQa2RRNndOVkdEbXpUQllRd1Z6YzY4dm9MSlBvWXlWZ24iLCJtYWMiOiJiNWFlMjZhMzcxZDVhODNkNTkyM2E3NDVlNjEwMTY1Y2NmMGQ2NzY4MGRjNDE5NTFhMzUwMTA4ODZkMDcwOGU0IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:35:53 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6IkRrekttd0RVOGw5aC9DSXJJWlVSNEE9PSIsInZhbHVlIjoiZmkwNkVJYk5rQnBxbTJlT0V2Y0lNTmd1S0p2eGpVeEdkdmdQL1V0c1NSM1BTaVpxbC9zWEVic2duL2VYNERxYVY1ajVPNGpEMnJ3c0NDS3dFMW5zay9MdDNQUkczMGNONlZoRHhYZkpuM2tMd0c4elFLekVwcDgwZG9qd0g5dkQiLCJtYWMiOiIzYWM1ZjA4Nzc2YjVlZmU5ODc5ZTE5ZWI0MmVhNjQzNmY5MTJjNzVmMmY1ODIyMmMyMmE1ZmExNzkzMTI3NDMxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:35:53 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-11674329\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-553677641 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>32</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"80 characters\">http://osool-b2g.test/_debugbar/open?id=X886a3b2013d31edc6c57bea8aa348118&amp;op=get</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-553677641\", {\"maxDepth\":0})</script>\n"}}