{"__meta": {"id": "Xb98c6286270cb8be8df3b88657409fb2", "datetime": "2025-07-31 14:22:01", "utime": **********.630661, "method": "GET", "uri": "/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 325, "messages": [{"message": "[14:21:36] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753960896.37825, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:36] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": 1753960896.457782, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:36] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": 1753960896.457835, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:36] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": 1753960896.45787, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.387799, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.38825, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.388313, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.388363, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.388404, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.388451, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.388501, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 231", "message_html": null, "is_string": false, "label": "warning", "time": **********.391238, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.391559, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.391602, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.391636, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.39167, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.391702, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.39174, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.391782, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 231", "message_html": null, "is_string": false, "label": "warning", "time": **********.392268, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.392323, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.392359, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.392392, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.392439, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.392471, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.392508, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.392547, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 231", "message_html": null, "is_string": false, "label": "warning", "time": **********.392819, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.392871, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.392905, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.392938, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.392969, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.393, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.393038, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.393075, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 231", "message_html": null, "is_string": false, "label": "warning", "time": **********.393354, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.39341, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.393447, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.393478, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.393512, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.393544, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.393581, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.393623, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 235", "message_html": null, "is_string": false, "label": "warning", "time": **********.39401, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.39406, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.394105, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.394136, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.39417, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.3942, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.394245, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.394283, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 235", "message_html": null, "is_string": false, "label": "warning", "time": **********.394553, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.394607, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.394641, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.394672, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.394703, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.394734, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.394769, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.394806, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 235", "message_html": null, "is_string": false, "label": "warning", "time": **********.395065, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.395115, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.395149, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.39518, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.395211, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.395241, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.395276, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.395313, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 235", "message_html": null, "is_string": false, "label": "warning", "time": **********.395569, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.395641, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.395675, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.395706, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.395737, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.395768, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.395804, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.395846, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 235", "message_html": null, "is_string": false, "label": "warning", "time": **********.396125, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.396173, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.396207, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.396238, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.39627, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.396303, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.396339, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.396376, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 239", "message_html": null, "is_string": false, "label": "warning", "time": **********.396611, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.396659, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.396705, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.39674, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.39677, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.3968, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.396835, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.396872, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 239", "message_html": null, "is_string": false, "label": "warning", "time": **********.397107, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.397155, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.397196, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.397227, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.397259, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.397289, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.397324, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.397361, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.397611, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.397659, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.397695, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.397727, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.397757, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.397787, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.397822, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.397859, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.398158, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.398207, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.398252, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.398285, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.398316, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.398346, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.398383, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.398422, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.398658, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.398706, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.398749, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.398781, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.398817, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.39885, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.398886, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getMovedStatusMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 133", "message_html": null, "is_string": false, "label": "warning", "time": **********.398922, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": **********.398954, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 155", "message_html": null, "is_string": false, "label": "warning", "time": **********.398994, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.399038, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.399276, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.399332, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.399372, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.399409, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.399441, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.399489, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.399673, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getMovedStatusMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 133", "message_html": null, "is_string": false, "label": "warning", "time": **********.399747, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": **********.399787, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 155", "message_html": null, "is_string": false, "label": "warning", "time": **********.399822, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.39986, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.40049, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.400565, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.40061, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.400644, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.400717, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.40076, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.4008, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getMovedStatusMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 133", "message_html": null, "is_string": false, "label": "warning", "time": **********.400837, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": **********.400869, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 155", "message_html": null, "is_string": false, "label": "warning", "time": **********.400903, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.400938, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.401191, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.401289, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.401327, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.401358, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.401389, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.401419, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.401463, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.401501, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.401736, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.401802, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.401849, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.401916, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.401946, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.401977, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.402012, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.40205, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.402281, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.402335, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.402395, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.402427, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.402457, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.402487, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.402522, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.402561, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.402792, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.402856, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.402889, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.40292, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.402951, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.402981, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.403016, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.403052, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.403281, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.403333, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.403376, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.403408, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.403438, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.403468, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.403503, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.403539, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.403768, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.403819, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.403854, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.403905, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.403938, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.403968, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.404014, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.404053, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.404297, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.404361, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.404401, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.404434, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.404483, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.404515, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.404552, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.404587, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.404825, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.404872, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.404923, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.404955, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.404985, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.405015, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.405049, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.405086, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.405314, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.405361, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.405399, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.40543, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.405461, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.405491, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.405527, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.405563, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.40579, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.405842, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.40588, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.405909, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.405941, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.405971, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.406007, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.406045, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.406266, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.406323, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.406355, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.406385, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.406415, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.406457, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.406493, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.40653, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.408671, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.408914, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.408998, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.409041, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.409084, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.409129, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.409172, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.409249, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.40972, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.409788, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.409844, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.409879, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.409913, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.409947, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.409988, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.41003, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.410296, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.410349, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.410388, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.410422, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.410463, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.410506, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.410543, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getMovedStatusMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 133", "message_html": null, "is_string": false, "label": "warning", "time": **********.410579, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": **********.410615, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 155", "message_html": null, "is_string": false, "label": "warning", "time": **********.410649, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.410688, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.410936, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.410993, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.411033, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.411082, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.411139, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.411172, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.41121, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.411248, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.411491, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.411544, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.411577, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.411609, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.41164, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.41167, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.411704, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getMovedStatusMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 133", "message_html": null, "is_string": false, "label": "warning", "time": **********.411739, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": **********.411768, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 155", "message_html": null, "is_string": false, "label": "warning", "time": **********.411804, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.411839, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.412162, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.412214, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.41226, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.412295, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.412327, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.412358, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.412392, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getMovedStatusMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 133", "message_html": null, "is_string": false, "label": "warning", "time": **********.412427, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 154", "message_html": null, "is_string": false, "label": "warning", "time": **********.412457, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::translateStatus is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 155", "message_html": null, "is_string": false, "label": "warning", "time": **********.412488, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.41252, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.412756, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.412805, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.41284, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.412871, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.412917, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.412949, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.412984, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.413022, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.413261, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.413313, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.41335, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.413381, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.413412, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.413442, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.413478, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.413514, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.413732, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::getLogLine is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\dcb335fef463da9248585226faee822e2b561232.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.413816, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::checkIfLogTypeManaged is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 40", "message_html": null, "is_string": false, "label": "warning", "time": **********.413849, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Accessing static trait property App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::$Api_log_type is deprecated, it should only be accessed on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 51", "message_html": null, "is_string": false, "label": "warning", "time": **********.413883, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareIcon is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 41", "message_html": null, "is_string": false, "label": "warning", "time": **********.413913, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareshortMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 42", "message_html": null, "is_string": false, "label": "warning", "time": **********.413941, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareLongMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 43", "message_html": null, "is_string": false, "label": "warning", "time": **********.413974, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::prepareDateMessage is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 44", "message_html": null, "is_string": false, "label": "warning", "time": **********.41401, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Calling static trait method App\\Http\\Traits\\CRMProjects\\ActiviteLogManagment::pluralizeArabic is deprecated, it should only be called on a class using the trait in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\CRMProjects\\ActiviteLogManagment.php on line 244", "message_html": null, "is_string": false, "label": "warning", "time": **********.414237, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2416", "message_html": null, "is_string": false, "label": "warning", "time": **********.430486, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2476", "message_html": null, "is_string": false, "label": "warning", "time": **********.43061, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3617", "message_html": null, "is_string": false, "label": "warning", "time": **********.431404, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3414", "message_html": null, "is_string": false, "label": "warning", "time": **********.511507, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.517228, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 636", "message_html": null, "is_string": false, "label": "warning", "time": **********.548633, "xdebug_link": null, "collector": "log"}, {"message": "[14:22:01] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c.php on line 3", "message_html": null, "is_string": false, "label": "warning", "time": **********.576302, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753960895.896496, "end": **********.631321, "duration": 25.734824895858765, "duration_str": "25.73s", "measures": [{"label": "Booting", "start": 1753960895.896496, "relative_start": 0, "end": 1753960896.35944, "relative_end": 1753960896.35944, "duration": 0.46294403076171875, "duration_str": "463ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753960896.359458, "relative_start": 0.46296191215515137, "end": **********.631323, "relative_end": 2.1457672119140625e-06, "duration": 25.271865129470825, "duration_str": "25.27s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 48709136, "peak_usage_str": "46MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 32, "templates": [{"name": "CRMProjects.project-details (\\resources\\views\\CRMProjects\\project-details.blade.php)", "param_count": 14, "params": ["projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.project-details (\\resources\\views\\livewire\\c-r-m-projects\\project-details.blade.php)", "param_count": 83, "params": ["projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "errors", "_instance", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.vendor-client-user-share (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\vendor-client-user-share.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.delete-confirm (\\resources\\views\\livewire\\c-r-m-projects\\modals\\delete-confirm.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.confirm-delete-file (\\resources\\views\\livewire\\c-r-m-projects\\modals\\confirm-delete-file.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.milestones (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\milestones.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.files (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\files.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.activity (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\activity.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.documents (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\documents.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.common.loader (\\resources\\views\\livewire\\common\\loader.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.common.pagination (\\resources\\views\\livewire\\common\\pagination.blade.php)", "param_count": 26, "params": ["errors", "_instance", "currentPage", "perPage", "totalRecords", "totalPages", "startIndex", "endIndex", "functionName", "showPerPage", "perPageV<PERSON>ues", "updateUrl", "listeners", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.setting (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\setting.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.bma_list (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\bma_list.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.sales.common.no-data-tr (\\resources\\views\\livewire\\sales\\common\\no-data-tr.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.inviteUser (\\resources\\views\\livewire\\c-r-m-projects\\modals\\inviteUser.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.shareClient (\\resources\\views\\livewire\\c-r-m-projects\\modals\\shareClient.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.shareVendors (\\resources\\views\\livewire\\c-r-m-projects\\modals\\shareVendors.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.assign_BMA (\\resources\\views\\livewire\\c-r-m-projects\\modals\\assign_BMA.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.CancelProjectAccessModal (\\resources\\views\\livewire\\c-r-m-projects\\modals\\CancelProjectAccessModal.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.delete-confirm-milstone (\\resources\\views\\livewire\\c-r-m-projects\\modals\\delete-confirm-milstone.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.modals.deleteAssignBMA (\\resources\\views\\livewire\\c-r-m-projects\\modals\\deleteAssignBMA.blade.php)", "param_count": 85, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.partials.details.balde_js_script (\\resources\\views\\livewire\\c-r-m-projects\\partials\\details\\balde_js_script.blade.php)", "param_count": 88, "params": ["__env", "app", "errors", "_instance", "projectData", "settingsState", "daysleft", "milestones_list", "files_list", "chartData", "projectID", "workspaceSlug", "attachment", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount", "__currentLoopData", "pt", "loop"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 18, "params": ["__env", "app", "errors", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "livewire.notifications.messages-notifications-list (\\resources\\views\\livewire\\notifications\\messages-notifications-list.blade.php)", "param_count": 23, "params": ["chatList", "errors", "_instance", "workspaceSlug", "totalUnreadNotifications", "previousUnreadCount", "newList", "list", "slugs", "userId", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials.check_crm_session (\\resources\\views\\layouts\\partials\\check_crm_session.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET CRMProjects/details/{id}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\CRMProjects\\ProjectController@details", "as": "CRMProjects.details", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/CRMProjects", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\CRMProjects\\ProjectController.php&line=19\">\\app\\Http\\Controllers\\CRMProjects\\ProjectController.php:19-32</a>"}, "queries": {"nb_statements": 16, "nb_failed_statements": 0, "accumulated_duration": 0.02251, "accumulated_duration_str": "22.51ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.00362, "duration_str": "3.62ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 16.082}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7368 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.0008900000000000001, "duration_str": "890μs", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_dev_db2", "start_percent": 16.082, "width_percent": 3.954}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.00078, "duration_str": "780μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_dev_db2", "start_percent": 20.036, "width_percent": 3.465}, {"sql": "select * from `release_notes` where `store_status` = 1 and `release_notes`.`deleted_at` is null group by `version`", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3046}, {"index": 15, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 51}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00272, "duration_str": "2.72ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3046", "connection": "osool_dev_db2", "start_percent": 23.501, "width_percent": 12.084}, {"sql": "select * from `crm_user` where `crm_user`.`user_id` = 7368 and `crm_user`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "9740534860d5c11a9cf9e73e8939e4083f82ba1d", "line": 122}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 122}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "view::9740534860d5c11a9cf9e73e8939e4083f82ba1d:122", "connection": "osool_dev_db2", "start_percent": 35.584, "width_percent": 2.888}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'admin' limit 1", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1927}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1927", "connection": "osool_dev_db2", "start_percent": 38.472, "width_percent": 4.931}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_dev_db2", "start_percent": 43.403, "width_percent": 3.554}, {"sql": "select * from `work_orders` where `project_user_id` = 7368", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\RequestedItemService.php", "line": 21}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 148}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 73}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0016200000000000001, "duration_str": "1.62ms", "stmt_id": "\\app\\Services\\RequestedItemService.php:21", "connection": "osool_dev_db2", "start_percent": 46.957, "width_percent": 7.197}, {"sql": "select count(*) as aggregate from `service_provider_missing_items_requests` where `status` = 'requested' and 0 = 1", "type": "query", "params": [], "bindings": ["requested"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\RequestedItemService.php", "line": 26}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 148}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 73}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Services\\RequestedItemService.php:26", "connection": "osool_dev_db2", "start_percent": 54.154, "width_percent": 1.91}, {"sql": "select `id`, `created_at` from `users` where `project_id` = 201 and `user_type` = 'admin' and `status` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 256}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 267}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 158}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 74}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00372, "duration_str": "3.72ms", "stmt_id": "\\app\\Http\\Traits\\UserTrait.php:256", "connection": "osool_dev_db2", "start_percent": 56.064, "width_percent": 16.526}, {"sql": "select count(*) as aggregate from `work_orders` inner join `contracts` on `contracts`.`id` = `work_orders`.`contract_id` inner join `property_buildings` on `property_buildings`.`id` = `work_orders`.`property_id` inner join `users` on `users`.`id` = `work_orders`.`created_by` where `work_orders`.`status` = 4 and `work_orders`.`is_deleted` = 'no' and `work_orders`.`start_date` <= '2025-07-31' and `work_orders`.`project_user_id` = 7368", "type": "query", "params": [], "bindings": ["4", "no", "2025-07-31", "7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\WorkOrdersTrait.php", "line": 2102}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 191}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 78}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00125, "duration_str": "1.25ms", "stmt_id": "\\app\\Http\\Traits\\WorkOrdersTrait.php:2102", "connection": "osool_dev_db2", "start_percent": 72.59, "width_percent": 5.553}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 892}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00068, "duration_str": "680μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 78.143, "width_percent": 3.021}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 892}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 81.164, "width_percent": 1.644}, {"sql": "select `id` from `users` where `project_id` = 201 and `user_type` = 'admin' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["201", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 928}, {"index": 14, "namespace": "view", "name": "f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c", "line": 13}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00311, "duration_str": "3.11ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:928", "connection": "osool_dev_db2", "start_percent": 82.808, "width_percent": 13.816}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 96.624, "width_percent": 1.644}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00039, "duration_str": "390μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 98.267, "width_percent": 1.733}]}, "models": {"data": {"App\\Models\\CrmUser": 1, "App\\Models\\ReleaseNotes": 1, "App\\Models\\ProjectsDetails": 2, "App\\Models\\UserCompany": 1, "App\\Models\\User": 2}, "count": 7}, "livewire": {"data": {"common.paginator #vwaUY7gJV9X31IQkMUf1": "array:5 [\n  \"data\" => array:11 [\n    \"currentPage\" => 1\n    \"perPage\" => 10\n    \"totalRecords\" => 1\n    \"totalPages\" => 1.0\n    \"startIndex\" => 1\n    \"endIndex\" => 1\n    \"functionName\" => \"fetchDocuments\"\n    \"showPerPage\" => false\n    \"perPageValues\" => array:3 [\n      0 => 10\n      1 => 20\n      2 => 40\n    ]\n    \"updateUrl\" => true\n    \"listeners\" => array:1 [\n      \"refreshPagination\" => \"mount\"\n    ]\n  ]\n  \"name\" => \"common.paginator\"\n  \"view\" => \"livewire.common.pagination\"\n  \"component\" => \"App\\Http\\Livewire\\Common\\Paginator\"\n  \"id\" => \"vwaUY7gJV9X31IQkMUf1\"\n]", "c-r-m-projects.project-details #sTmOKCAE0qZBudJITX5B": "array:5 [\n  \"data\" => array:67 [\n    \"projectID\" => 635\n    \"workspaceSlug\" => \"khansaa-test34444\"\n    \"projectData\" => array:37 [\n      \"id\" => 635\n      \"type\" => \"project\"\n      \"project_type\" => null\n      \"priority_level\" => null\n      \"title\" => \"dup 1\"\n      \"status\" => \"Draft\"\n      \"description\" => \"mohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammad mohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammadmohammad\"\n      \"start_date\" => \"2025-05-28\"\n      \"end_date\" => \"2025-05-30\"\n      \"cost\" => 2555\n      \"tags\" => null\n      \"estimated_hrs\" => 0\n      \"currency\" => \"$\"\n      \"project_progress\" => \"false\"\n      \"progress\" => \"0\"\n      \"task_progress\" => \"true\"\n      \"total_task\" => 9\n      \"total_comment\" => 0\n      \"created_by\" => \"<EMAIL>\"\n      \"budget\" => \" 2.56K ﷼\"\n      \"users\" => array:1 [\n        0 => array:5 [\n          \"id\" => 65\n          \"name\" => \"Khansa<PERSON> Hasan\"\n          \"email\" => \"<EMAIL>\"\n          \"complate_task\" => \"9/9\"\n          \"avatar\" => \"https://workdo-dev.osool.cloud/uploads/users-avatar/avatar.png\"\n        ]\n      ]\n      \"clients\" => []\n      \"milestones\" => array:5 [\n        0 => array:10 [\n          \"id\" => 562\n          \"title\" => \"TEST\"\n          \"status\" => \"incomplete\"\n          \"start_date\" => \"2025-07-02\"\n          \"end_date\" => \"2026-07-02\"\n          \"progress\" => 0\n          \"summary\" => \"SUMMERY\"\n          \"cost\" => \" 1.99K ﷼\"\n          \"created_at\" => \"2025-07-02T08:26:33.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:04:36.000000Z\"\n        ]\n        1 => array:10 [\n          \"id\" => 563\n          \"title\" => \"MILESTONE2\"\n          \"status\" => \"incomplete\"\n          \"start_date\" => \"2025-07-02\"\n          \"end_date\" => \"2025-07-03\"\n          \"progress\" => 0\n          \"summary\" => \"MILESTONE\"\n          \"cost\" => \" 1.2K ﷼\"\n          \"created_at\" => \"2025-07-02T08:28:43.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:04:36.000000Z\"\n        ]\n        2 => array:10 [\n          \"id\" => 577\n          \"title\" => \"MILESTONE3\"\n          \"status\" => \"incomplete\"\n          \"start_date\" => \"2025-07-01\"\n          \"end_date\" => \"2025-07-31\"\n          \"progress\" => 0\n          \"summary\" => \"TEST\"\n          \"cost\" => \" 100.00 ﷼\"\n          \"created_at\" => \"2025-07-15T10:06:56.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:04:36.000000Z\"\n        ]\n        3 => array:10 [\n          \"id\" => 578\n          \"title\" => \"MS4\"\n          \"status\" => \"complete\"\n          \"start_date\" => \"2025-07-01\"\n          \"end_date\" => \"2025-07-31\"\n          \"progress\" => 100\n          \"summary\" => \"SUMMERY\"\n          \"cost\" => \" 100.00 ﷼\"\n          \"created_at\" => \"2025-07-15T10:15:04.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:58:42.000000Z\"\n        ]\n        4 => array:10 [\n          \"id\" => 579\n          \"title\" => \"MS5\"\n          \"status\" => \"complete\"\n          \"start_date\" => \"2025-07-01\"\n          \"end_date\" => \"2025-07-31\"\n          \"progress\" => 100\n          \"summary\" => \"SUMMARY\"\n          \"cost\" => \" 100.00 ﷼\"\n          \"created_at\" => \"2025-07-15T10:37:30.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:42:16.000000Z\"\n        ]\n      ]\n      \"vendors\" => []\n      \"buildingManager\" => []\n      \"files\" => array:1 [\n        0 => array:5 [\n          \"id\" => 1151\n          \"file_name\" => \"Hasan Khansaa - Article Summary.pdf\"\n          \"file_path\" => \"https://workdo-dev.osool.cloud/uploads/projects/Hasan Khansaa - Article Summary_1753623526.pdf\"\n          \"created_at\" => \"2025-07-27T13:38:46.000000Z\"\n          \"updated_at\" => \"2025-07-27T13:38:46.000000Z\"\n        ]\n      ]\n      \"activities\" => array:37 [\n        0 => array:9 [\n          \"id\" => 4529\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new Task123\"\n          \"created_at\" => \"2025-07-31T10:58:30.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:58:30.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"123\"\n          ]\n        ]\n        1 => array:9 [\n          \"id\" => 4528\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new Task123\"\n          \"created_at\" => \"2025-07-31T10:55:16.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:55:16.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"123\"\n          ]\n        ]\n        2 => array:9 [\n          \"id\" => 4527\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskNDDDFFFF123\"\n          \"created_at\" => \"2025-07-31T10:47:42.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:47:42.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"NDDDFFFF123\"\n          ]\n        ]\n        3 => array:9 [\n          \"id\" => 4526\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskNDDDFFFF\"\n          \"created_at\" => \"2025-07-31T10:38:31.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:38:31.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"NDDDFFFF\"\n          ]\n        ]\n        4 => array:9 [\n          \"id\" => 4525\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTEST345123\"\n          \"created_at\" => \"2025-07-31T10:14:05.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:14:05.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TEST345123\"\n          ]\n        ]\n        5 => array:9 [\n          \"id\" => 4524\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTEST345\"\n          \"created_at\" => \"2025-07-31T10:07:41.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:07:41.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TEST345\"\n          ]\n        ]\n        6 => array:9 [\n          \"id\" => 4523\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTESTG123\"\n          \"created_at\" => \"2025-07-31T10:03:00.000000Z\"\n          \"updated_at\" => \"2025-07-31T10:03:00.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TESTG123\"\n          ]\n        ]\n        7 => array:9 [\n          \"id\" => 4521\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTESTG\"\n          \"created_at\" => \"2025-07-31T09:24:31.000000Z\"\n          \"updated_at\" => \"2025-07-31T09:24:31.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TESTG\"\n          ]\n        ]\n        8 => array:9 [\n          \"id\" => 4519\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTTRREEE\"\n          \"created_at\" => \"2025-07-31T09:10:02.000000Z\"\n          \"updated_at\" => \"2025-07-31T09:10:02.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TTRREEE\"\n          ]\n        ]\n        9 => array:9 [\n          \"id\" => 4468\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Upload File\"\n          \"log_info\" => \" Upload new fileHasan Khansaa - Article Summary.pdf\"\n          \"created_at\" => \"2025-07-27T13:38:46.000000Z\"\n          \"updated_at\" => \"2025-07-27T13:38:46.000000Z\"\n          \"remark\" => array:1 [\n            \"file_name\" => \"Hasan Khansaa - Article Summary.pdf\"\n          ]\n        ]\n        10 => array:9 [\n          \"id\" => 4466\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTESTING NOTIFICATIONS\"\n          \"created_at\" => \"2025-07-27T12:04:42.000000Z\"\n          \"updated_at\" => \"2025-07-27T12:04:42.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TESTING NOTIFICATIONS\"\n          ]\n        ]\n        11 => array:9 [\n          \"id\" => 4422\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new Taskdrag task33\"\n          \"created_at\" => \"2025-07-23T12:51:33.000000Z\"\n          \"updated_at\" => \"2025-07-23T12:51:33.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"drag task33\"\n          ]\n        ]\n        12 => array:9 [\n          \"id\" => 4419\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTask call22\"\n          \"created_at\" => \"2025-07-23T12:45:03.000000Z\"\n          \"updated_at\" => \"2025-07-23T12:45:03.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"Task call22\"\n          ]\n        ]\n        13 => array:9 [\n          \"id\" => 4379\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new Taskdrag task\"\n          \"created_at\" => \"2025-07-21T09:23:22.000000Z\"\n          \"updated_at\" => \"2025-07-21T09:23:22.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"drag task\"\n          ]\n        ]\n        14 => array:9 [\n          \"id\" => 4378\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Move\"\n          \"log_info\" => \" Move TaskTEST NOTIFICATIONfrom Done to In Progress\"\n          \"created_at\" => \"2025-07-21T08:56:29.000000Z\"\n          \"updated_at\" => \"2025-07-21T08:56:29.000000Z\"\n          \"remark\" => array:3 [\n            \"title\" => \"TEST NOTIFICATION\"\n            \"old_status\" => \"Done\"\n            \"new_status\" => \"In Progress\"\n          ]\n        ]\n        15 => array:9 [\n          \"id\" => 4377\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Move\"\n          \"log_info\" => \" Move TaskTEST123from Done to In Progress\"\n          \"created_at\" => \"2025-07-21T08:56:20.000000Z\"\n          \"updated_at\" => \"2025-07-21T08:56:20.000000Z\"\n          \"remark\" => array:3 [\n            \"title\" => \"TEST123\"\n            \"old_status\" => \"Done\"\n            \"new_status\" => \"In Progress\"\n          ]\n        ]\n        16 => array:9 [\n          \"id\" => 4376\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Move\"\n          \"log_info\" => \" Move TaskTEST TASKfrom Done to In Progress\"\n          \"created_at\" => \"2025-07-21T08:56:08.000000Z\"\n          \"updated_at\" => \"2025-07-21T08:56:08.000000Z\"\n          \"remark\" => array:3 [\n            \"title\" => \"TEST TASK\"\n            \"old_status\" => \"Done\"\n            \"new_status\" => \"In Progress\"\n          ]\n        ]\n        17 => array:9 [\n          \"id\" => 4337\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTASK5\"\n          \"created_at\" => \"2025-07-15T11:54:16.000000Z\"\n          \"updated_at\" => \"2025-07-15T11:54:16.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TASK5\"\n          ]\n        ]\n        18 => array:9 [\n          \"id\" => 4336\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskAccusantium voluptas\"\n          \"created_at\" => \"2025-07-15T11:50:17.000000Z\"\n          \"updated_at\" => \"2025-07-15T11:50:17.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"Accusantium voluptas\"\n          ]\n        ]\n        19 => array:9 [\n          \"id\" => 4335\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTSK4\"\n          \"created_at\" => \"2025-07-15T11:45:58.000000Z\"\n          \"updated_at\" => \"2025-07-15T11:45:58.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TSK4\"\n          ]\n        ]\n        20 => array:9 [\n          \"id\" => 4334\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskSed ullam voluptates\"\n          \"created_at\" => \"2025-07-15T11:43:31.000000Z\"\n          \"updated_at\" => \"2025-07-15T11:43:31.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"Sed ullam voluptates\"\n          ]\n        ]\n        21 => array:9 [\n          \"id\" => 4333\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new Tasktest0\"\n          \"created_at\" => \"2025-07-15T11:02:21.000000Z\"\n          \"updated_at\" => \"2025-07-15T11:02:21.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"test0\"\n          ]\n        ]\n        22 => array:9 [\n          \"id\" => 4332\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTSK4\"\n          \"created_at\" => \"2025-07-15T10:58:42.000000Z\"\n          \"updated_at\" => \"2025-07-15T10:58:42.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TSK4\"\n          ]\n        ]\n        23 => array:9 [\n          \"id\" => 4331\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTSK3\"\n          \"created_at\" => \"2025-07-15T10:43:18.000000Z\"\n          \"updated_at\" => \"2025-07-15T10:43:18.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TSK3\"\n          ]\n        ]\n        24 => array:9 [\n          \"id\" => 4330\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTSK2\"\n          \"created_at\" => \"2025-07-15T10:38:15.000000Z\"\n          \"updated_at\" => \"2025-07-15T10:38:15.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TSK2\"\n          ]\n        ]\n        25 => array:9 [\n          \"id\" => 4329\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Milestone\"\n          \"log_info\" => \" Create new MilestoneMS5\"\n          \"created_at\" => \"2025-07-15T10:37:30.000000Z\"\n          \"updated_at\" => \"2025-07-15T10:37:30.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"MS5\"\n          ]\n        ]\n        26 => array:9 [\n          \"id\" => 4328\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTSK1\"\n          \"created_at\" => \"2025-07-15T10:16:23.000000Z\"\n          \"updated_at\" => \"2025-07-15T10:16:23.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TSK1\"\n          ]\n        ]\n        27 => array:9 [\n          \"id\" => 4327\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Milestone\"\n          \"log_info\" => \" Create new MilestoneMS4\"\n          \"created_at\" => \"2025-07-15T10:15:04.000000Z\"\n          \"updated_at\" => \"2025-07-15T10:15:04.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"MS4\"\n          ]\n        ]\n        28 => array:9 [\n          \"id\" => 4326\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTEST NOTIFICATION\"\n          \"created_at\" => \"2025-07-15T10:07:26.000000Z\"\n          \"updated_at\" => \"2025-07-15T10:07:26.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TEST NOTIFICATION\"\n          ]\n        ]\n        29 => array:9 [\n          \"id\" => 4325\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Milestone\"\n          \"log_info\" => \" Create new MilestoneMILESTONE3\"\n          \"created_at\" => \"2025-07-15T10:06:56.000000Z\"\n          \"updated_at\" => \"2025-07-15T10:06:56.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"MILESTONE3\"\n          ]\n        ]\n        30 => array:9 [\n          \"id\" => 3874\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Move\"\n          \"log_info\" => \" Move TaskTEST123from Tangible WO to Todo\"\n          \"created_at\" => \"2025-07-06T08:38:45.000000Z\"\n          \"updated_at\" => \"2025-07-06T08:38:45.000000Z\"\n          \"remark\" => array:3 [\n            \"title\" => \"TEST123\"\n            \"old_status\" => \"Tangible WO\"\n            \"new_status\" => \"Todo\"\n          ]\n        ]\n        31 => array:9 [\n          \"id\" => 3873\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTEST123\"\n          \"created_at\" => \"2025-07-06T08:37:43.000000Z\"\n          \"updated_at\" => \"2025-07-06T08:37:43.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TEST123\"\n          ]\n        ]\n        32 => array:9 [\n          \"id\" => 3868\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Move\"\n          \"log_info\" => \" Move TaskTEST TASKfrom Review to Done\"\n          \"created_at\" => \"2025-07-02T08:45:19.000000Z\"\n          \"updated_at\" => \"2025-07-02T08:45:19.000000Z\"\n          \"remark\" => array:3 [\n            \"title\" => \"TEST TASK\"\n            \"old_status\" => \"Review\"\n            \"new_status\" => \"Done\"\n          ]\n        ]\n        33 => array:9 [\n          \"id\" => 3867\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Move\"\n          \"log_info\" => \" Move TaskTEST TASKfrom Tangible WO to Review\"\n          \"created_at\" => \"2025-07-02T08:45:07.000000Z\"\n          \"updated_at\" => \"2025-07-02T08:45:07.000000Z\"\n          \"remark\" => array:3 [\n            \"title\" => \"TEST TASK\"\n            \"old_status\" => \"Tangible WO\"\n            \"new_status\" => \"Review\"\n          ]\n        ]\n        34 => array:9 [\n          \"id\" => 3866\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Task\"\n          \"log_info\" => \" Create new TaskTEST TASK\"\n          \"created_at\" => \"2025-07-02T08:44:04.000000Z\"\n          \"updated_at\" => \"2025-07-02T08:44:04.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TEST TASK\"\n          ]\n        ]\n        35 => array:9 [\n          \"id\" => 3865\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Milestone\"\n          \"log_info\" => \" Create new MilestoneMILESTONE2\"\n          \"created_at\" => \"2025-07-02T08:28:43.000000Z\"\n          \"updated_at\" => \"2025-07-02T08:28:43.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"MILESTONE2\"\n          ]\n        ]\n        36 => array:9 [\n          \"id\" => 3864\n          \"user_id\" => 65\n          \"user_name\" => \"Khansaa Hasan\"\n          \"user_type\" => \"App\\Models\\User\"\n          \"log_type\" => \"Create Milestone\"\n          \"log_info\" => \" Create new MilestoneTEST\"\n          \"created_at\" => \"2025-07-02T08:26:33.000000Z\"\n          \"updated_at\" => \"2025-07-02T08:26:33.000000Z\"\n          \"remark\" => array:1 [\n            \"title\" => \"TEST\"\n          ]\n        ]\n      ]\n      \"documents\" => array:1 [\n        0 => array:10 [\n          \"id\" => 131\n          \"subject\" => \"document CRF\"\n          \"user_name\" => \"Staff Test\"\n          \"type\" => \"test\"\n          \"notes\" => null\n          \"status\" => \"pending\"\n          \"description\" => null\n          \"file_path\" => \"https://workdo-dev.osool.cloud/\"\n          \"created_at\" => \"2025-07-27T13:32:34.000000Z\"\n          \"updated_at\" => \"2025-07-27T13:32:34.000000Z\"\n        ]\n      ]\n      \"project_setting\" => array:18 [\n        \"basic_details\" => \"on\"\n        \"member\" => \"on\"\n        \"client\" => \"on\"\n        \"vendor\" => \"on\"\n        \"milestone\" => \"on\"\n        \"activity\" => \"on\"\n        \"attachment\" => \"on\"\n        \"bug_report\" => \"on\"\n        \"task\" => \"on\"\n        \"invoice\" => \"on\"\n        \"bill\" => \"on\"\n        \"documents\" => \"on\"\n        \"timesheet\" => \"on\"\n        \"progress\" => \"on\"\n        \"retainer\" => \"on\"\n        \"proposal\" => \"on\"\n        \"password_protected\" => \"off\"\n        \"procurement\" => \"on\"\n      ]\n      \"bma_users\" => array:2 [\n        0 => array:3 [\n          \"id\" => 403\n          \"name\" => \"BMA\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        1 => array:3 [\n          \"id\" => 407\n          \"name\" => \"bma\"\n          \"email\" => \"<EMAIL>\"\n        ]\n      ]\n      \"get_assigned_users\" => []\n      \"all_users\" => array:7 [\n        0 => array:3 [\n          \"id\" => 65\n          \"name\" => \"Khansaa Hasan\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        1 => array:3 [\n          \"id\" => 209\n          \"name\" => \"Kirsten Mclaughlin\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        2 => array:3 [\n          \"id\" => 212\n          \"name\" => \"Staff Test\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        3 => array:3 [\n          \"id\" => 218\n          \"name\" => \"Khansaa Hasan22\"\n          \"email\" => \"khansaaha995@33gail.2com\"\n        ]\n        4 => array:3 [\n          \"id\" => 383\n          \"name\" => \"NAEL2\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        5 => array:3 [\n          \"id\" => 403\n          \"name\" => \"BMA\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        6 => array:3 [\n          \"id\" => 407\n          \"name\" => \"bma\"\n          \"email\" => \"<EMAIL>\"\n        ]\n      ]\n      \"all_clients\" => array:57 [\n        0 => array:3 [\n          \"id\" => 219\n          \"name\" => \"Test deal\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        1 => array:3 [\n          \"id\" => 318\n          \"name\" => \"Test Client\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        2 => array:3 [\n          \"id\" => 323\n          \"name\" => \"Alhayajneh Mohammad\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        3 => array:3 [\n          \"id\" => 492\n          \"name\" => \"Customer 1\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        4 => array:3 [\n          \"id\" => 493\n          \"name\" => \"Customer 2\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        5 => array:3 [\n          \"id\" => 494\n          \"name\" => \"Customer 3\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        6 => array:3 [\n          \"id\" => 495\n          \"name\" => \"Customer 4\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        7 => array:3 [\n          \"id\" => 496\n          \"name\" => \"Customer 5\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        8 => array:3 [\n          \"id\" => 497\n          \"name\" => \"Customer 6\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        9 => array:3 [\n          \"id\" => 498\n          \"name\" => \"Customer 7\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        10 => array:3 [\n          \"id\" => 499\n          \"name\" => \"Customer 8\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        11 => array:3 [\n          \"id\" => 500\n          \"name\" => \"Customer 9\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        12 => array:3 [\n          \"id\" => 501\n          \"name\" => \"Customer 10\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        13 => array:3 [\n          \"id\" => 502\n          \"name\" => \"Customer 11\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        14 => array:3 [\n          \"id\" => 503\n          \"name\" => \"Customer 12\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        15 => array:3 [\n          \"id\" => 504\n          \"name\" => \"Customer 13\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        16 => array:3 [\n          \"id\" => 505\n          \"name\" => \"Customer 14\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        17 => array:3 [\n          \"id\" => 506\n          \"name\" => \"Customer 15\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        18 => array:3 [\n          \"id\" => 507\n          \"name\" => \"Customer 16\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        19 => array:3 [\n          \"id\" => 508\n          \"name\" => \"Customer 17\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        20 => array:3 [\n          \"id\" => 509\n          \"name\" => \"Customer 18\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        21 => array:3 [\n          \"id\" => 510\n          \"name\" => \"Customer 19\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        22 => array:3 [\n          \"id\" => 511\n          \"name\" => \"Customer 20\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        23 => array:3 [\n          \"id\" => 512\n          \"name\" => \"Customer 21\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        24 => array:3 [\n          \"id\" => 513\n          \"name\" => \"Customer 22\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        25 => array:3 [\n          \"id\" => 514\n          \"name\" => \"Customer 23\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        26 => array:3 [\n          \"id\" => 515\n          \"name\" => \"Customer 24\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        27 => array:3 [\n          \"id\" => 516\n          \"name\" => \"Customer 25\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        28 => array:3 [\n          \"id\" => 517\n          \"name\" => \"Customer 26\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        29 => array:3 [\n          \"id\" => 518\n          \"name\" => \"Customer 27\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        30 => array:3 [\n          \"id\" => 519\n          \"name\" => \"Customer 28\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        31 => array:3 [\n          \"id\" => 520\n          \"name\" => \"Customer 29\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        32 => array:3 [\n          \"id\" => 521\n          \"name\" => \"Customer 30\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        33 => array:3 [\n          \"id\" => 522\n          \"name\" => \"Customer 31\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        34 => array:3 [\n          \"id\" => 523\n          \"name\" => \"Customer 32\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        35 => array:3 [\n          \"id\" => 524\n          \"name\" => \"Customer 33\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        36 => array:3 [\n          \"id\" => 525\n          \"name\" => \"Customer 34\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        37 => array:3 [\n          \"id\" => 526\n          \"name\" => \"Customer 35\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        38 => array:3 [\n          \"id\" => 527\n          \"name\" => \"Customer 36\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        39 => array:3 [\n          \"id\" => 528\n          \"name\" => \"Customer 37\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        40 => array:3 [\n          \"id\" => 529\n          \"name\" => \"Customer 38\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        41 => array:3 [\n          \"id\" => 530\n          \"name\" => \"Customer 39\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        42 => array:3 [\n          \"id\" => 531\n          \"name\" => \"Customer 40\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        43 => array:3 [\n          \"id\" => 532\n          \"name\" => \"Customer 41\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        44 => array:3 [\n          \"id\" => 533\n          \"name\" => \"Customer 42\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        45 => array:3 [\n          \"id\" => 534\n          \"name\" => \"Customer 43\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        46 => array:3 [\n          \"id\" => 535\n          \"name\" => \"Customer 44\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        47 => array:3 [\n          \"id\" => 536\n          \"name\" => \"Customer 45\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        48 => array:3 [\n          \"id\" => 537\n          \"name\" => \"Customer 46\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        49 => array:3 [\n          \"id\" => 538\n          \"name\" => \"Customer 47\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        50 => array:3 [\n          \"id\" => 539\n          \"name\" => \"Customer 48\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        51 => array:3 [\n          \"id\" => 540\n          \"name\" => \"Customer 49\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        52 => array:3 [\n          \"id\" => 541\n          \"name\" => \"Customer 50\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        53 => array:3 [\n          \"id\" => 563\n          \"name\" => \"Khansaa 18.06\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        54 => array:3 [\n          \"id\" => 564\n          \"name\" => \"Fouzan\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        55 => array:3 [\n          \"id\" => 631\n          \"name\" => \"Testing\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        56 => array:3 [\n          \"id\" => 652\n          \"name\" => \"New Tenant Dev Server\"\n          \"email\" => \"<EMAIL>\"\n        ]\n      ]\n      \"all_vendors\" => array:59 [\n        0 => array:3 [\n          \"id\" => 491\n          \"name\" => \"Fouzan\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        1 => array:3 [\n          \"id\" => 568\n          \"name\" => \"Test Vendor 1\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        2 => array:3 [\n          \"id\" => 569\n          \"name\" => \"Test Vendor 2\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        3 => array:3 [\n          \"id\" => 570\n          \"name\" => \"Test Vendor 3\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        4 => array:3 [\n          \"id\" => 571\n          \"name\" => \"Test Vendor 4\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        5 => array:3 [\n          \"id\" => 572\n          \"name\" => \"Test Vendor 5\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        6 => array:3 [\n          \"id\" => 573\n          \"name\" => \"Test Vendor 6\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        7 => array:3 [\n          \"id\" => 574\n          \"name\" => \"Test Vendor 7\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        8 => array:3 [\n          \"id\" => 575\n          \"name\" => \"Test Vendor 8\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        9 => array:3 [\n          \"id\" => 576\n          \"name\" => \"Test Vendor 9\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        10 => array:3 [\n          \"id\" => 577\n          \"name\" => \"Test Vendor 10\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        11 => array:3 [\n          \"id\" => 578\n          \"name\" => \"Test Vendor 11\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        12 => array:3 [\n          \"id\" => 579\n          \"name\" => \"Test Vendor 12\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        13 => array:3 [\n          \"id\" => 580\n          \"name\" => \"Test Vendor 13\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        14 => array:3 [\n          \"id\" => 581\n          \"name\" => \"Test Vendor 14\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        15 => array:3 [\n          \"id\" => 582\n          \"name\" => \"Test Vendor 15\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        16 => array:3 [\n          \"id\" => 583\n          \"name\" => \"Test Vendor 16\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        17 => array:3 [\n          \"id\" => 584\n          \"name\" => \"Test Vendor 17\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        18 => array:3 [\n          \"id\" => 585\n          \"name\" => \"Test Vendor 18\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        19 => array:3 [\n          \"id\" => 586\n          \"name\" => \"Test Vendor 19\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        20 => array:3 [\n          \"id\" => 587\n          \"name\" => \"Test Vendor 20\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        21 => array:3 [\n          \"id\" => 588\n          \"name\" => \"Test Vendor 21\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        22 => array:3 [\n          \"id\" => 589\n          \"name\" => \"Test Vendor 22\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        23 => array:3 [\n          \"id\" => 590\n          \"name\" => \"Test Vendor 23\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        24 => array:3 [\n          \"id\" => 591\n          \"name\" => \"Test Vendor 24\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        25 => array:3 [\n          \"id\" => 592\n          \"name\" => \"Test Vendor 25\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        26 => array:3 [\n          \"id\" => 593\n          \"name\" => \"Test Vendor 26\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        27 => array:3 [\n          \"id\" => 594\n          \"name\" => \"Test Vendor 27\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        28 => array:3 [\n          \"id\" => 595\n          \"name\" => \"Test Vendor 28\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        29 => array:3 [\n          \"id\" => 596\n          \"name\" => \"Test Vendor 29\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        30 => array:3 [\n          \"id\" => 597\n          \"name\" => \"Test Vendor 30\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        31 => array:3 [\n          \"id\" => 598\n          \"name\" => \"Test Vendor 31\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        32 => array:3 [\n          \"id\" => 599\n          \"name\" => \"Test Vendor 32\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        33 => array:3 [\n          \"id\" => 600\n          \"name\" => \"Test Vendor 33\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        34 => array:3 [\n          \"id\" => 601\n          \"name\" => \"Test Vendor 34\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        35 => array:3 [\n          \"id\" => 602\n          \"name\" => \"Test Vendor 35\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        36 => array:3 [\n          \"id\" => 603\n          \"name\" => \"Test Vendor 36\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        37 => array:3 [\n          \"id\" => 604\n          \"name\" => \"Test Vendor 37\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        38 => array:3 [\n          \"id\" => 605\n          \"name\" => \"Test Vendor 38\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        39 => array:3 [\n          \"id\" => 606\n          \"name\" => \"Test Vendor 39\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        40 => array:3 [\n          \"id\" => 607\n          \"name\" => \"Test Vendor 40\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        41 => array:3 [\n          \"id\" => 608\n          \"name\" => \"Test Vendor 41\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        42 => array:3 [\n          \"id\" => 609\n          \"name\" => \"Test Vendor 42\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        43 => array:3 [\n          \"id\" => 610\n          \"name\" => \"Test Vendor 43\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        44 => array:3 [\n          \"id\" => 611\n          \"name\" => \"Test Vendor 44\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        45 => array:3 [\n          \"id\" => 612\n          \"name\" => \"Test Vendor 45\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        46 => array:3 [\n          \"id\" => 613\n          \"name\" => \"Test Vendor 46\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        47 => array:3 [\n          \"id\" => 614\n          \"name\" => \"Test Vendor 47\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        48 => array:3 [\n          \"id\" => 615\n          \"name\" => \"Test Vendor 48\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        49 => array:3 [\n          \"id\" => 616\n          \"name\" => \"Test Vendor 49\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        50 => array:3 [\n          \"id\" => 617\n          \"name\" => \"Test Vendor 50\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        51 => array:3 [\n          \"id\" => 618\n          \"name\" => \"Test Vendor 51\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        52 => array:3 [\n          \"id\" => 619\n          \"name\" => \"Test Vendor 52\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        53 => array:3 [\n          \"id\" => 620\n          \"name\" => \"Test Vendor 53\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        54 => array:3 [\n          \"id\" => 621\n          \"name\" => \"Test Vendor 54\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        55 => array:3 [\n          \"id\" => 622\n          \"name\" => \"Test Vendor\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        56 => array:3 [\n          \"id\" => 628\n          \"name\" => \"Test Vendor\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        57 => array:3 [\n          \"id\" => 629\n          \"name\" => \"Test Vendor\"\n          \"email\" => \"<EMAIL>\"\n        ]\n        58 => array:3 [\n          \"id\" => 653\n          \"name\" => \"SP Admin Test Dev\"\n          \"email\" => \"<EMAIL>\"\n        ]\n      ]\n      \"document_types\" => array:1 [\n        0 => array:6 [\n          \"id\" => 6\n          \"name\" => \"test\"\n          \"workspace_id\" => 46\n          \"created_by\" => 65\n          \"created_at\" => \"2025-04-08T09:06:36.000000Z\"\n          \"updated_at\" => \"2025-04-08T09:06:36.000000Z\"\n        ]\n      ]\n      \"priority_levels\" => array:4 [\n        0 => array:2 [\n          \"value\" => \"critical\"\n          \"label\" => \"Critical\"\n        ]\n        1 => array:2 [\n          \"value\" => \"high\"\n          \"label\" => \"High\"\n        ]\n        2 => array:2 [\n          \"value\" => \"medium\"\n          \"label\" => \"Medium\"\n        ]\n        3 => array:2 [\n          \"value\" => \"low\"\n          \"label\" => \"Low\"\n        ]\n      ]\n      \"project_types\" => array:8 [\n        0 => array:2 [\n          \"value\" => \"new_construction\"\n          \"label\" => \"New Construction\"\n        ]\n        1 => array:2 [\n          \"value\" => \"renovation\"\n          \"label\" => \"Renovation\"\n        ]\n        2 => array:2 [\n          \"value\" => \"maintenance\"\n          \"label\" => \"Maintenance\"\n        ]\n        3 => array:2 [\n          \"value\" => \"expansion\"\n          \"label\" => \"Expansion\"\n        ]\n        4 => array:2 [\n          \"value\" => \"demolition\"\n          \"label\" => \"Demolition\"\n        ]\n        5 => array:2 [\n          \"value\" => \"infrastructure\"\n          \"label\" => \"Infrastructure\"\n        ]\n        6 => array:2 [\n          \"value\" => \"design_projects\"\n          \"label\" => \"Design Projects\"\n        ]\n        7 => array:2 [\n          \"value\" => \"compliance_safety\"\n          \"label\" => \"Compliance & Safety\"\n        ]\n      ]\n    ]\n    \"attachment\" => null\n    \"chartData\" => array:8 [\n      101 => array:7 [\n        0 => 0\n        1 => 0\n        2 => 0\n        3 => 0\n        4 => 0\n        5 => 0\n        6 => 0\n      ]\n      102 => array:7 [\n        0 => 0\n        1 => 0\n        2 => 0\n        3 => 0\n        4 => 0\n        5 => 0\n        6 => 0\n      ]\n      103 => array:7 [\n        0 => 0\n        1 => 0\n        2 => 0\n        3 => 0\n        4 => 0\n        5 => 0\n        6 => 0\n      ]\n      104 => array:7 [\n        0 => 0\n        1 => 0\n        2 => 0\n        3 => 0\n        4 => 0\n        5 => 0\n        6 => 0\n      ]\n      462 => array:7 [\n        0 => 0\n        1 => 0\n        2 => 0\n        3 => 0\n        4 => 0\n        5 => 0\n        6 => 0\n      ]\n      \"label\" => array:7 [\n        0 => \"Mon\"\n        1 => \"Tue\"\n        2 => \"Wed\"\n        3 => \"Thu\"\n        4 => \"Fri\"\n        5 => \"Sat\"\n        6 => \"Sun\"\n      ]\n      \"color\" => array:5 [\n        0 => \"#000000\"\n        1 => \"#77b6ea\"\n        2 => \"#545454\"\n        3 => \"#3cb8d9\"\n        4 => \"#37b37e\"\n      ]\n      \"stages\" => array:5 [\n        101 => \"Todo\"\n        102 => \"In Progress\"\n        103 => \"Review\"\n        104 => \"Done\"\n        462 => \"Tangible WO\"\n      ]\n    ]\n    \"daysleft\" => -62\n    \"start_date\" => null\n    \"filenametoDelete\" => null\n    \"progress\" => 0\n    \"end_date\" => null\n    \"budget\" => null\n    \"description\" => null\n    \"name\" => null\n    \"input_name\" => null\n    \"users\" => array:7 [\n      0 => array:3 [\n        \"id\" => 65\n        \"name\" => \"Khansaa Hasan\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      1 => array:3 [\n        \"id\" => 209\n        \"name\" => \"Kirsten Mclaughlin\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      2 => array:3 [\n        \"id\" => 212\n        \"name\" => \"Staff Test\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      3 => array:3 [\n        \"id\" => 218\n        \"name\" => \"Khansaa Hasan22\"\n        \"email\" => \"khansaaha995@33gail.2com\"\n      ]\n      4 => array:3 [\n        \"id\" => 383\n        \"name\" => \"NAEL2\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      5 => array:3 [\n        \"id\" => 403\n        \"name\" => \"BMA\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      6 => array:3 [\n        \"id\" => 407\n        \"name\" => \"bma\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"project_type\" => null\n    \"projectType\" => array:8 [\n      0 => array:2 [\n        \"value\" => \"new_construction\"\n        \"label\" => \"New Construction\"\n      ]\n      1 => array:2 [\n        \"value\" => \"renovation\"\n        \"label\" => \"Renovation\"\n      ]\n      2 => array:2 [\n        \"value\" => \"maintenance\"\n        \"label\" => \"Maintenance\"\n      ]\n      3 => array:2 [\n        \"value\" => \"expansion\"\n        \"label\" => \"Expansion\"\n      ]\n      4 => array:2 [\n        \"value\" => \"demolition\"\n        \"label\" => \"Demolition\"\n      ]\n      5 => array:2 [\n        \"value\" => \"infrastructure\"\n        \"label\" => \"Infrastructure\"\n      ]\n      6 => array:2 [\n        \"value\" => \"design_projects\"\n        \"label\" => \"Design Projects\"\n      ]\n      7 => array:2 [\n        \"value\" => \"compliance_safety\"\n        \"label\" => \"Compliance & Safety\"\n      ]\n    ]\n    \"priorityLevel\" => array:4 [\n      0 => array:2 [\n        \"value\" => \"critical\"\n        \"label\" => \"Critical\"\n      ]\n      1 => array:2 [\n        \"value\" => \"high\"\n        \"label\" => \"High\"\n      ]\n      2 => array:2 [\n        \"value\" => \"medium\"\n        \"label\" => \"Medium\"\n      ]\n      3 => array:2 [\n        \"value\" => \"low\"\n        \"label\" => \"Low\"\n      ]\n    ]\n    \"priority_level\" => null\n    \"projectExists\" => true\n    \"isModalOpen\" => false\n    \"attachments\" => []\n    \"documentTypes\" => array:1 [\n      0 => array:6 [\n        \"id\" => 6\n        \"name\" => \"test\"\n        \"workspace_id\" => 46\n        \"created_by\" => 65\n        \"created_at\" => \"2025-04-08T09:06:36.000000Z\"\n        \"updated_at\" => \"2025-04-08T09:06:36.000000Z\"\n      ]\n    ]\n    \"projects\" => []\n    \"DocumentsPaginated\" => array:5 [\n      \"items\" => array:1 [\n        0 => array:17 [\n          \"id\" => 131\n          \"subject\" => \"document CRF\"\n          \"user_id\" => 212\n          \"user_name\" => \"Staff Test\"\n          \"project_id\" => 635\n          \"project_name\" => \"dup 1\"\n          \"type\" => 6\n          \"type_name\" => \"test\"\n          \"notes\" => null\n          \"status\" => \"pending\"\n          \"description\" => \"document\"\n          \"additional_description\" => null\n          \"slug\" => \"document\"\n          \"workspace_id\" => 46\n          \"created_by\" => 65\n          \"created_at\" => \"2025-07-27T13:32:34.000000Z\"\n          \"updated_at\" => \"2025-07-27T13:32:34.000000Z\"\n        ]\n      ]\n      \"next_page_url\" => null\n      \"prev_page_url\" => null\n      \"per_page\" => 10\n      \"total\" => 1\n    ]\n    \"files\" => []\n    \"isDeleteModalOpen\" => false\n    \"filePaths\" => []\n    \"folderKey\" => \"uploads\"\n    \"maxFileSize\" => 5120\n    \"attachmentFiles\" => null\n    \"currentDate\" => null\n    \"user\" => null\n    \"settingsState\" => array:19 [\n      \"basic_details\" => 1\n      \"member\" => 1\n      \"client\" => 1\n      \"vendor\" => 1\n      \"milestone\" => 1\n      \"activity\" => 1\n      \"attachment\" => 1\n      \"task\" => 1\n      \"bug_report\" => 1\n      \"invoice\" => 1\n      \"bill\" => 1\n      \"timesheet\" => 1\n      \"documents\" => 1\n      \"progress\" => 1\n      \"password_protected\" => 0\n      \"password\" => 0\n      \"retainer\" => 1\n      \"proposal\" => 1\n      \"procurement\" => 1\n    ]\n    \"milestoneData\" => array:5 [\n      0 => array:10 [\n        \"id\" => 562\n        \"title\" => \"TEST\"\n        \"status\" => \"incomplete\"\n        \"start_date\" => \"2025-07-02\"\n        \"end_date\" => \"2026-07-02\"\n        \"progress\" => 0\n        \"summary\" => \"SUMMERY\"\n        \"cost\" => \" 1.99K ﷼\"\n        \"created_at\" => \"2025-07-02T08:26:33.000000Z\"\n        \"updated_at\" => \"2025-07-31T10:04:36.000000Z\"\n      ]\n      1 => array:10 [\n        \"id\" => 563\n        \"title\" => \"MILESTONE2\"\n        \"status\" => \"incomplete\"\n        \"start_date\" => \"2025-07-02\"\n        \"end_date\" => \"2025-07-03\"\n        \"progress\" => 0\n        \"summary\" => \"MILESTONE\"\n        \"cost\" => \" 1.2K ﷼\"\n        \"created_at\" => \"2025-07-02T08:28:43.000000Z\"\n        \"updated_at\" => \"2025-07-31T10:04:36.000000Z\"\n      ]\n      2 => array:10 [\n        \"id\" => 577\n        \"title\" => \"MILESTONE3\"\n        \"status\" => \"incomplete\"\n        \"start_date\" => \"2025-07-01\"\n        \"end_date\" => \"2025-07-31\"\n        \"progress\" => 0\n        \"summary\" => \"TEST\"\n        \"cost\" => \" 100.00 ﷼\"\n        \"created_at\" => \"2025-07-15T10:06:56.000000Z\"\n        \"updated_at\" => \"2025-07-31T10:04:36.000000Z\"\n      ]\n      3 => array:10 [\n        \"id\" => 578\n        \"title\" => \"MS4\"\n        \"status\" => \"complete\"\n        \"start_date\" => \"2025-07-01\"\n        \"end_date\" => \"2025-07-31\"\n        \"progress\" => 100\n        \"summary\" => \"SUMMERY\"\n        \"cost\" => \" 100.00 ﷼\"\n        \"created_at\" => \"2025-07-15T10:15:04.000000Z\"\n        \"updated_at\" => \"2025-07-31T10:58:42.000000Z\"\n      ]\n      4 => array:10 [\n        \"id\" => 579\n        \"title\" => \"MS5\"\n        \"status\" => \"complete\"\n        \"start_date\" => \"2025-07-01\"\n        \"end_date\" => \"2025-07-31\"\n        \"progress\" => 100\n        \"summary\" => \"SUMMARY\"\n        \"cost\" => \" 100.00 ﷼\"\n        \"created_at\" => \"2025-07-15T10:37:30.000000Z\"\n        \"updated_at\" => \"2025-07-31T10:42:16.000000Z\"\n      ]\n    ]\n    \"status\" => \"incomplete\"\n    \"statuss\" => null\n    \"cost\" => null\n    \"summary\" => null\n    \"fileData\" => []\n    \"type\" => null\n    \"user_id\" => null\n    \"subject\" => null\n    \"notes\" => null\n    \"project\" => null\n    \"document_id\" => null\n    \"projectDetails\" => null\n    \"selectedUsersForInvite\" => []\n    \"usersAlreadyInvited\" => array:1 [\n      0 => \"<EMAIL>\"\n    ]\n    \"selectedvendorsForShare\" => []\n    \"vendorsAlreadyInProject\" => []\n    \"selectedclientsForShare\" => []\n    \"clientssAlreadyInProject\" => []\n    \"clients\" => array:57 [\n      0 => array:3 [\n        \"id\" => 219\n        \"name\" => \"Test deal\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      1 => array:3 [\n        \"id\" => 318\n        \"name\" => \"Test Client\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      2 => array:3 [\n        \"id\" => 323\n        \"name\" => \"Alhayajneh Mohammad\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      3 => array:3 [\n        \"id\" => 492\n        \"name\" => \"Customer 1\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      4 => array:3 [\n        \"id\" => 493\n        \"name\" => \"Customer 2\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      5 => array:3 [\n        \"id\" => 494\n        \"name\" => \"Customer 3\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      6 => array:3 [\n        \"id\" => 495\n        \"name\" => \"Customer 4\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      7 => array:3 [\n        \"id\" => 496\n        \"name\" => \"Customer 5\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      8 => array:3 [\n        \"id\" => 497\n        \"name\" => \"Customer 6\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      9 => array:3 [\n        \"id\" => 498\n        \"name\" => \"Customer 7\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      10 => array:3 [\n        \"id\" => 499\n        \"name\" => \"Customer 8\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      11 => array:3 [\n        \"id\" => 500\n        \"name\" => \"Customer 9\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      12 => array:3 [\n        \"id\" => 501\n        \"name\" => \"Customer 10\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      13 => array:3 [\n        \"id\" => 502\n        \"name\" => \"Customer 11\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      14 => array:3 [\n        \"id\" => 503\n        \"name\" => \"Customer 12\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      15 => array:3 [\n        \"id\" => 504\n        \"name\" => \"Customer 13\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      16 => array:3 [\n        \"id\" => 505\n        \"name\" => \"Customer 14\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      17 => array:3 [\n        \"id\" => 506\n        \"name\" => \"Customer 15\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      18 => array:3 [\n        \"id\" => 507\n        \"name\" => \"Customer 16\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      19 => array:3 [\n        \"id\" => 508\n        \"name\" => \"Customer 17\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      20 => array:3 [\n        \"id\" => 509\n        \"name\" => \"Customer 18\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      21 => array:3 [\n        \"id\" => 510\n        \"name\" => \"Customer 19\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      22 => array:3 [\n        \"id\" => 511\n        \"name\" => \"Customer 20\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      23 => array:3 [\n        \"id\" => 512\n        \"name\" => \"Customer 21\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      24 => array:3 [\n        \"id\" => 513\n        \"name\" => \"Customer 22\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      25 => array:3 [\n        \"id\" => 514\n        \"name\" => \"Customer 23\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      26 => array:3 [\n        \"id\" => 515\n        \"name\" => \"Customer 24\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      27 => array:3 [\n        \"id\" => 516\n        \"name\" => \"Customer 25\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      28 => array:3 [\n        \"id\" => 517\n        \"name\" => \"Customer 26\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      29 => array:3 [\n        \"id\" => 518\n        \"name\" => \"Customer 27\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      30 => array:3 [\n        \"id\" => 519\n        \"name\" => \"Customer 28\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      31 => array:3 [\n        \"id\" => 520\n        \"name\" => \"Customer 29\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      32 => array:3 [\n        \"id\" => 521\n        \"name\" => \"Customer 30\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      33 => array:3 [\n        \"id\" => 522\n        \"name\" => \"Customer 31\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      34 => array:3 [\n        \"id\" => 523\n        \"name\" => \"Customer 32\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      35 => array:3 [\n        \"id\" => 524\n        \"name\" => \"Customer 33\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      36 => array:3 [\n        \"id\" => 525\n        \"name\" => \"Customer 34\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      37 => array:3 [\n        \"id\" => 526\n        \"name\" => \"Customer 35\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      38 => array:3 [\n        \"id\" => 527\n        \"name\" => \"Customer 36\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      39 => array:3 [\n        \"id\" => 528\n        \"name\" => \"Customer 37\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      40 => array:3 [\n        \"id\" => 529\n        \"name\" => \"Customer 38\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      41 => array:3 [\n        \"id\" => 530\n        \"name\" => \"Customer 39\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      42 => array:3 [\n        \"id\" => 531\n        \"name\" => \"Customer 40\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      43 => array:3 [\n        \"id\" => 532\n        \"name\" => \"Customer 41\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      44 => array:3 [\n        \"id\" => 533\n        \"name\" => \"Customer 42\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      45 => array:3 [\n        \"id\" => 534\n        \"name\" => \"Customer 43\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      46 => array:3 [\n        \"id\" => 535\n        \"name\" => \"Customer 44\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      47 => array:3 [\n        \"id\" => 536\n        \"name\" => \"Customer 45\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      48 => array:3 [\n        \"id\" => 537\n        \"name\" => \"Customer 46\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      49 => array:3 [\n        \"id\" => 538\n        \"name\" => \"Customer 47\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      50 => array:3 [\n        \"id\" => 539\n        \"name\" => \"Customer 48\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      51 => array:3 [\n        \"id\" => 540\n        \"name\" => \"Customer 49\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      52 => array:3 [\n        \"id\" => 541\n        \"name\" => \"Customer 50\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      53 => array:3 [\n        \"id\" => 563\n        \"name\" => \"Khansaa 18.06\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      54 => array:3 [\n        \"id\" => 564\n        \"name\" => \"Fouzan\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      55 => array:3 [\n        \"id\" => 631\n        \"name\" => \"Testing\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      56 => array:3 [\n        \"id\" => 652\n        \"name\" => \"New Tenant Dev Server\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"vendors\" => array:59 [\n      0 => array:3 [\n        \"id\" => 491\n        \"name\" => \"Fouzan\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      1 => array:3 [\n        \"id\" => 568\n        \"name\" => \"Test Vendor 1\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      2 => array:3 [\n        \"id\" => 569\n        \"name\" => \"Test Vendor 2\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      3 => array:3 [\n        \"id\" => 570\n        \"name\" => \"Test Vendor 3\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      4 => array:3 [\n        \"id\" => 571\n        \"name\" => \"Test Vendor 4\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      5 => array:3 [\n        \"id\" => 572\n        \"name\" => \"Test Vendor 5\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      6 => array:3 [\n        \"id\" => 573\n        \"name\" => \"Test Vendor 6\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      7 => array:3 [\n        \"id\" => 574\n        \"name\" => \"Test Vendor 7\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      8 => array:3 [\n        \"id\" => 575\n        \"name\" => \"Test Vendor 8\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      9 => array:3 [\n        \"id\" => 576\n        \"name\" => \"Test Vendor 9\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      10 => array:3 [\n        \"id\" => 577\n        \"name\" => \"Test Vendor 10\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      11 => array:3 [\n        \"id\" => 578\n        \"name\" => \"Test Vendor 11\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      12 => array:3 [\n        \"id\" => 579\n        \"name\" => \"Test Vendor 12\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      13 => array:3 [\n        \"id\" => 580\n        \"name\" => \"Test Vendor 13\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      14 => array:3 [\n        \"id\" => 581\n        \"name\" => \"Test Vendor 14\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      15 => array:3 [\n        \"id\" => 582\n        \"name\" => \"Test Vendor 15\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      16 => array:3 [\n        \"id\" => 583\n        \"name\" => \"Test Vendor 16\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      17 => array:3 [\n        \"id\" => 584\n        \"name\" => \"Test Vendor 17\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      18 => array:3 [\n        \"id\" => 585\n        \"name\" => \"Test Vendor 18\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      19 => array:3 [\n        \"id\" => 586\n        \"name\" => \"Test Vendor 19\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      20 => array:3 [\n        \"id\" => 587\n        \"name\" => \"Test Vendor 20\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      21 => array:3 [\n        \"id\" => 588\n        \"name\" => \"Test Vendor 21\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      22 => array:3 [\n        \"id\" => 589\n        \"name\" => \"Test Vendor 22\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      23 => array:3 [\n        \"id\" => 590\n        \"name\" => \"Test Vendor 23\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      24 => array:3 [\n        \"id\" => 591\n        \"name\" => \"Test Vendor 24\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      25 => array:3 [\n        \"id\" => 592\n        \"name\" => \"Test Vendor 25\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      26 => array:3 [\n        \"id\" => 593\n        \"name\" => \"Test Vendor 26\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      27 => array:3 [\n        \"id\" => 594\n        \"name\" => \"Test Vendor 27\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      28 => array:3 [\n        \"id\" => 595\n        \"name\" => \"Test Vendor 28\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      29 => array:3 [\n        \"id\" => 596\n        \"name\" => \"Test Vendor 29\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      30 => array:3 [\n        \"id\" => 597\n        \"name\" => \"Test Vendor 30\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      31 => array:3 [\n        \"id\" => 598\n        \"name\" => \"Test Vendor 31\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      32 => array:3 [\n        \"id\" => 599\n        \"name\" => \"Test Vendor 32\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      33 => array:3 [\n        \"id\" => 600\n        \"name\" => \"Test Vendor 33\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      34 => array:3 [\n        \"id\" => 601\n        \"name\" => \"Test Vendor 34\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      35 => array:3 [\n        \"id\" => 602\n        \"name\" => \"Test Vendor 35\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      36 => array:3 [\n        \"id\" => 603\n        \"name\" => \"Test Vendor 36\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      37 => array:3 [\n        \"id\" => 604\n        \"name\" => \"Test Vendor 37\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      38 => array:3 [\n        \"id\" => 605\n        \"name\" => \"Test Vendor 38\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      39 => array:3 [\n        \"id\" => 606\n        \"name\" => \"Test Vendor 39\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      40 => array:3 [\n        \"id\" => 607\n        \"name\" => \"Test Vendor 40\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      41 => array:3 [\n        \"id\" => 608\n        \"name\" => \"Test Vendor 41\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      42 => array:3 [\n        \"id\" => 609\n        \"name\" => \"Test Vendor 42\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      43 => array:3 [\n        \"id\" => 610\n        \"name\" => \"Test Vendor 43\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      44 => array:3 [\n        \"id\" => 611\n        \"name\" => \"Test Vendor 44\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      45 => array:3 [\n        \"id\" => 612\n        \"name\" => \"Test Vendor 45\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      46 => array:3 [\n        \"id\" => 613\n        \"name\" => \"Test Vendor 46\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      47 => array:3 [\n        \"id\" => 614\n        \"name\" => \"Test Vendor 47\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      48 => array:3 [\n        \"id\" => 615\n        \"name\" => \"Test Vendor 48\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      49 => array:3 [\n        \"id\" => 616\n        \"name\" => \"Test Vendor 49\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      50 => array:3 [\n        \"id\" => 617\n        \"name\" => \"Test Vendor 50\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      51 => array:3 [\n        \"id\" => 618\n        \"name\" => \"Test Vendor 51\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      52 => array:3 [\n        \"id\" => 619\n        \"name\" => \"Test Vendor 52\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      53 => array:3 [\n        \"id\" => 620\n        \"name\" => \"Test Vendor 53\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      54 => array:3 [\n        \"id\" => 621\n        \"name\" => \"Test Vendor 54\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      55 => array:3 [\n        \"id\" => 622\n        \"name\" => \"Test Vendor\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      56 => array:3 [\n        \"id\" => 628\n        \"name\" => \"Test Vendor\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      57 => array:3 [\n        \"id\" => 629\n        \"name\" => \"Test Vendor\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      58 => array:3 [\n        \"id\" => 653\n        \"name\" => \"SP Admin Test Dev\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"fileType\" => 1\n    \"fileLanguage\" => 1\n    \"bmas_list\" => []\n    \"assignedBMAList\" => []\n    \"projectAccessUserId\" => 0\n    \"projectAccessDeleteMessage\" => \"\"\n    \"projectAccessUserName\" => \"\"\n    \"projectAccessUserType\" => \"\"\n    \"selectedBuildingsManager\" => []\n    \"allBuildingsManager\" => array:2 [\n      0 => array:3 [\n        \"id\" => 403\n        \"name\" => \"BMA\"\n        \"email\" => \"<EMAIL>\"\n      ]\n      1 => array:3 [\n        \"id\" => 407\n        \"name\" => \"bma\"\n        \"email\" => \"<EMAIL>\"\n      ]\n    ]\n    \"deleteAssignBMA\" => array:2 [\n      \"bma_id\" => \"\"\n      \"bma_name\" => \"\"\n    ]\n    \"currentPage\" => []\n  ]\n  \"name\" => \"c-r-m-projects.project-details\"\n  \"view\" => \"livewire.c-r-m-projects.project-details\"\n  \"component\" => \"App\\Http\\Livewire\\CRMProjects\\ProjectDetails\"\n  \"id\" => \"sTmOKCAE0qZBudJITX5B\"\n]", "notifications.messages-notifications-list #dpPc4mbiWwUK9PpJ253F": "array:5 [\n  \"data\" => array:7 [\n    \"workspaceSlug\" => \"khansaa-test34444\"\n    \"totalUnreadNotifications\" => 0\n    \"previousUnreadCount\" => 0\n    \"newList\" => null\n    \"list\" => []\n    \"slugs\" => array:3 [\n      0 => \"facebook\"\n      1 => \"whatsapp\"\n      2 => \"instagram\"\n    ]\n    \"userId\" => null\n  ]\n  \"name\" => \"notifications.messages-notifications-list\"\n  \"view\" => \"livewire.notifications.messages-notifications-list\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\MessagesNotificationsList\"\n  \"id\" => \"dpPc4mbiWwUK9PpJ253F\"\n]", "notifications.new-notifications-list-top-nav #07gv59DLjuECHNDYxMU5": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => null\n    \"perPage\" => null\n    \"assignedAsset\" => null\n    \"contractsIds\" => null\n    \"accessBuildingsIds\" => null\n    \"currentDate\" => null\n    \"currentDateTime\" => null\n    \"readyToLoad\" => null\n    \"configOciLink\" => null\n    \"ociLink\" => null\n    \"selectedLanguage\" => null\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"07gv59DLjuECHNDYxMU5\"\n]", "menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#3968\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:69 [\n        \"id\" => 7368\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$Nrgb2fubI18xCSRqaKjUjOzVJbptwn80b0U.ID9srqgW/v2G.7cpy\"\n        \"name\" => \"Crm Test 02\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => \"99090\"\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 201\n        \"project_user_id\" => 7368\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-26 20:35:15\"\n        \"modified_at\" => \"2025-07-31 14:21:32\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khansaa-test34444\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzOTYwODkyLCJleHAiOjE3NTM5NjQ0OTIsIm5iZiI6MTc1Mzk2MDg5MiwianRpIjoiRlVoMm1aNW9xU2d0RndGYyIsInN1YiI6IjY1IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.qrdNorP6qIa77Qz_asTsi_NHIig--fVAVIEOzAh81UM\"\n      ]\n      #original: array:69 [\n        \"id\" => 7368\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$Nrgb2fubI18xCSRqaKjUjOzVJbptwn80b0U.ID9srqgW/v2G.7cpy\"\n        \"name\" => \"Crm Test 02\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => \"99090\"\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 201\n        \"project_user_id\" => 7368\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-26 20:35:15\"\n        \"modified_at\" => \"2025-07-31 14:21:32\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => \"khansaa-test34444\"\n        \"crm_api_token\" => \"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzOTYwODkyLCJleHAiOjE3NTM5NjQ0OTIsIm5iZiI6MTc1Mzk2MDg5MiwianRpIjoiRlVoMm1aNW9xU2d0RndGYyIsInN1YiI6IjY1IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.qrdNorP6qIa77Qz_asTsi_NHIig--fVAVIEOzAh81UM\"\n      ]\n      #changes: []\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:3 [\n        \"userCompany\" => App\\Models\\UserCompany {#3975\n          #connection: \"mysql\"\n          #table: \"user_company\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 360\n            \"user_id\" => 7368\n            \"company_id\" => 264\n            \"created_at\" => \"2025-02-26 20:35:21\"\n            \"updated_at\" => \"2025-02-26 20:35:21\"\n          ]\n          #original: array:5 [\n            \"id\" => 360\n            \"user_id\" => 7368\n            \"company_id\" => 264\n            \"created_at\" => \"2025-02-26 20:35:21\"\n            \"updated_at\" => \"2025-02-26 20:35:21\"\n          ]\n          #changes: []\n          #casts: array:2 [\n            \"user_id\" => \"int\"\n            \"company_id\" => \"int\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: array:2 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n          ]\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"company_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#3994\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:22 [\n            \"id\" => 201\n            \"user_id\" => 0\n            \"project_name\" => \"Khadeer CRM Test 02\"\n            \"project_name_ar\" => \"Khadeer CRM Test 02\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-26 17:34:01\"\n            \"updated_at\" => \"2025-04-23 13:56:58\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:22 [\n            \"id\" => 201\n            \"user_id\" => 0\n            \"project_name\" => \"Khadeer CRM Test 02\"\n            \"project_name_ar\" => \"Khadeer CRM Test 02\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-26 17:34:01\"\n            \"updated_at\" => \"2025-04-23 13:56:58\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n        \"crmUser\" => App\\Models\\CrmUser {#4230\n          #connection: \"mysql\"\n          #table: \"crm_user\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:8 [\n            \"id\" => 2\n            \"user_id\" => 7368\n            \"crm_user_id\" => 65\n            \"created_at\" => \"2025-02-26 20:35:15\"\n            \"updated_at\" => \"2025-02-26 20:35:15\"\n            \"instagram_connect\" => 1\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n          ]\n          #original: array:8 [\n            \"id\" => 2\n            \"user_id\" => 7368\n            \"crm_user_id\" => 65\n            \"created_at\" => \"2025-02-26 20:35:15\"\n            \"updated_at\" => \"2025-02-26 20:35:15\"\n            \"instagram_connect\" => 1\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"crm_user_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:54 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 7368\n    \"projectId\" => null\n    \"project\" => App\\Models\\ProjectsDetails {#4254\n      #connection: \"mysql\"\n      #table: \"projects_details\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 201\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khadeer CRM Test 02\"\n        \"project_name_ar\" => \"Khadeer CRM Test 02\"\n        \"use_crm_module\" => 1\n      ]\n      #original: array:9 [\n        \"id\" => 201\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khadeer CRM Test 02\"\n        \"project_name_ar\" => \"Khadeer CRM Test 02\"\n        \"use_crm_module\" => 1\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:16 [\n        0 => \"user_id\"\n        1 => \"project_name\"\n        2 => \"project_name_ar\"\n        3 => \"project_image\"\n        4 => \"industry_type\"\n        5 => \"created_by\"\n        6 => \"is_deleted\"\n        7 => \"use_erp_module\"\n        8 => \"use_tenant_module\"\n        9 => \"tenant_status\"\n        10 => \"use_beneficiary_module\"\n        11 => \"benificiary_status\"\n        12 => \"community_status\"\n        13 => \"contract_status\"\n        14 => \"share_post\"\n        15 => \"use_crm_module\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n    }\n    \"workOrderMenuItemColor\" => \"#000\"\n    \"flagWorkorderSidebarMenu\" => false\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => 0\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => null\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 5}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL", "captcha_answer": "32", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "ar", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-1515005361 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1515005361\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-662996760 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-662996760\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1175781875 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1175781875\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-993354167 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"242 characters\">http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IklmbllVR1FpbDFmKzBsWUppM0ZrTWc9PSIsInZhbHVlIjoickxqc0s4UFk0QzZYOGRPTE5URHM2YTNTTGNTRnhSa1hoV3hPMjNxZG5PZEJ3TFplSnl4SVYydEpBdnlZZjZwcEprMUxEclJVL1BlY2VNZll0TG5RalBwek44aHpsUzhCRUwza1VLbkhZUnE3cGgrdWVydy9USDVDMUNnemUrU3UiLCJtYWMiOiI3NDMxODA2ZmU4YjAzN2IwMTMyMjNhMmZjM2Y5M2UwMmYwMTdmNWE5OTlmMDY5YzNlYTUyZTBlZGQ2MWQ5ZjY3IiwidGFnIjoiIn0%3D; osool_session=sT1FrHAKwxleVZdTYuHDCIa1OK8uZ178oTRN4hNr</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-993354167\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-662577830 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"242 characters\">http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6IklmbllVR1FpbDFmKzBsWUppM0ZrTWc9PSIsInZhbHVlIjoickxqc0s4UFk0QzZYOGRPTE5URHM2YTNTTGNTRnhSa1hoV3hPMjNxZG5PZEJ3TFplSnl4SVYydEpBdnlZZjZwcEprMUxEclJVL1BlY2VNZll0TG5RalBwek44aHpsUzhCRUwza1VLbkhZUnE3cGgrdWVydy9USDVDMUNnemUrU3UiLCJtYWMiOiI3NDMxODA2ZmU4YjAzN2IwMTMyMjNhMmZjM2Y5M2UwMmYwMTdmNWE5OTlmMDY5YzNlYTUyZTBlZGQ2MWQ5ZjY3IiwidGFnIjoiIn0%3D; osool_session=sT1FrHAKwxleVZdTYuHDCIa1OK8uZ178oTRN4hNr</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">61073</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"221 characters\">/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"221 characters\">/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753960895.8965</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753960895</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-662577830\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-481886700 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-481886700\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1971447329 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:21:36 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6Imhwb0JoRnM3Z1krNnRhRzJ1bmVVN1E9PSIsInZhbHVlIjoiQzJ6aDg3Z1JCRU5OUGVWdmJ2a0JPUVhsMFpsRTFDakh3d3pMRXRGbWxBbm50Y0JRVW9Fb0tYWmZraklMSUhVMmZadjNaaDNPQ3BrVWpITTlJZDF0LzZnVlVGOFZwT1Y4VzVYSHlIMzZhaG9saW1NeXJDVWVldHdEakRkQWh5aSsiLCJtYWMiOiI1MDYwMThlNWMyZGNjZmM3MDIyZDJiZWU0OTlmOTQwZjZmMmYwZDk4MmQxYTE2OWFiNDdiZDY3YzBkN2M4OTg5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:22:01 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6InlWSzlRTGVUQUJVajFLdmF2TVgzRWc9PSIsInZhbHVlIjoiT0E1ZlgyRDdVWHNsZWFLa0dqV1VoOGZGVzU3TllWSnZsTlB3VUhxTm1MRllTMVNYUkJ5Q2Q4T2htMEpUeHEvSTJRMy9WRmhBMktYVFBQTkFtdlJjdVdLZXlIOXFEbmV2WWlIRlEweVNhWE4vdzVtN1ZScjdHS2lTV3BZQmtoU3kiLCJtYWMiOiIwMGZiZDRmYTFmZGU3OGIxMjA3ODg1M2RhZDZlMzg3YTA2OTQ4ZjdkNTAxOTA4Y2E5NDdiOGYxODJlNzQxZjEzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:22:01 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6Imhwb0JoRnM3Z1krNnRhRzJ1bmVVN1E9PSIsInZhbHVlIjoiQzJ6aDg3Z1JCRU5OUGVWdmJ2a0JPUVhsMFpsRTFDakh3d3pMRXRGbWxBbm50Y0JRVW9Fb0tYWmZraklMSUhVMmZadjNaaDNPQ3BrVWpITTlJZDF0LzZnVlVGOFZwT1Y4VzVYSHlIMzZhaG9saW1NeXJDVWVldHdEakRkQWh5aSsiLCJtYWMiOiI1MDYwMThlNWMyZGNjZmM3MDIyZDJiZWU0OTlmOTQwZjZmMmYwZDk4MmQxYTE2OWFiNDdiZDY3YzBkN2M4OTg5IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:22:01 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6InlWSzlRTGVUQUJVajFLdmF2TVgzRWc9PSIsInZhbHVlIjoiT0E1ZlgyRDdVWHNsZWFLa0dqV1VoOGZGVzU3TllWSnZsTlB3VUhxTm1MRllTMVNYUkJ5Q2Q4T2htMEpUeHEvSTJRMy9WRmhBMktYVFBQTkFtdlJjdVdLZXlIOXFEbmV2WWlIRlEweVNhWE4vdzVtN1ZScjdHS2lTV3BZQmtoU3kiLCJtYWMiOiIwMGZiZDRmYTFmZGU3OGIxMjA3ODg1M2RhZDZlMzg3YTA2OTQ4ZjdkNTAxOTA4Y2E5NDdiOGYxODJlNzQxZjEzIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:22:01 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1971447329\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1240152945 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>32</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"242 characters\">http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1240152945\", {\"maxDepth\":0})</script>\n"}}