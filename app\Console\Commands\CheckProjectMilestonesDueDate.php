<?php

namespace App\Console\Commands;

use App\Services\CRM\CRMUsers;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use App\Models\Notification;
use App\Mail\MilestoneMail;

class CheckProjectMilestonesDueDate extends Command
{
    /**
     * Number of days to check ahead for upcoming milestones
     */
    private const DAYS_AHEAD = 2;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'crm-project:check-milestones-due-date';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Check project milestones due dates and send notifications for approaching, due today, and overdue milestones';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle(): void
    {
        $token = $this->login();

        if (!$token) {
            logger()->error('Daily API Request: Login failed');
            return;
        }

        // Check upcoming milestones (approaching and due today)
        $upcomingOptions = [
            'due_after' => \Carbon\Carbon::now()->format('Y-m-d'),
            'due_before' => \Carbon\Carbon::now()->addDays(self::DAYS_AHEAD)->format('Y-m-d'),
        ];
        $upComingMileStonesList = $this->makeRequest($token, $upcomingOptions);
        $this->checkMilestoneDueDates($upComingMileStonesList);

        // Check overdue milestones
        $overdueOptions = [
            'due_before' => \Carbon\Carbon::now()->subDay()->format('Y-m-d'),
        ];
        $overdueMilestonesList = $this->makeRequest($token, $overdueOptions);
        $this->checkOverdueMilestones($overdueMilestonesList);
    }

    private function login(): ?string
    {
        $response = Http::post(env('CRM_API_BASE_URL') . '/api/login', [
            'email' => env('CRM_API_EMAIL'),
            'password' => env('CRM_API_PASSWORD'),
        ]);

        if ($response->successful() && isset($response->json()['authorisation']['token'])) {
            return $response->json()['authorisation']['token'];
        }

        return null;
    }

    private function makeRequest(string $token, array $options = []): array
    {
        try {
            $fullUrl = env('CRM_API_BASE_URL') . '/api/upcoming/milestones';

            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . $token,
                'Accept' => 'application/json',
            ])->get($fullUrl, $options);

            return $response->json() ?? [];
        } catch (\Exception $e) {
            Log::error('API Request Failed:', [
                'endpoint' => $fullUrl,
                'error' => $e->getMessage()
            ]);

            return [
                'status' => 'error',
                'message' => $e->getMessage()
            ];
        }
    }

    private function checkMilestoneDueDates(array $response)
    {
        if (!isset($response['data']) || !is_array($response['data'])) {
            Log::error('Invalid response structure', $response);
            return;
        }

        $today = \Carbon\Carbon::today();

        foreach ($response['data'] as $item) {
            try {
                $dueDate = \Carbon\Carbon::parse($item['milestone']['due_date']);
                $daysRemaining = $today->diffInDays($dueDate, false);

                if ($daysRemaining === 0) {
                    // Due today
                    $this->sendNotifications($item, 'due_today', $daysRemaining);
                } elseif ($daysRemaining > 0 && $daysRemaining <= self::DAYS_AHEAD) {
                    // Approaching (due in 1-2 days)
                    $this->sendNotifications($item, 'approaching', $daysRemaining);
                }
            } catch (\Exception $e) {
                Log::error('Error processing milestone', [
                    'item' => $item,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    private function checkOverdueMilestones(array $response)
    {
        if (!isset($response['data']) || !is_array($response['data'])) {
            Log::error('Invalid overdue response structure', $response);
            return;
        }

        foreach ($response['data'] as $item) {
            try {
                $this->sendNotifications($item, 'overdue', 0);
            } catch (\Exception $e) {
                Log::error('Error processing overdue milestone', [
                    'item' => $item,
                    'error' => $e->getMessage()
                ]);
            }
        }
    }

    private function sendNotifications(array $item, string $notificationType, int $daysRemaining)
    {
        $userIds = array_column($item['user'], 'id');

        // Get actual user instances (modify based on your user provider)
        $usersToNotify = CRMUsers::whereIn('crm_user_id', $userIds)->get();

        // Prepare milestone data for notifications
        $milestoneData = [
            'milestone' => $item['milestone'],
            'project' => $item['project'],
            'days_remaining' => $daysRemaining
        ];

        // Prepare translation parameters
        $translationParams = [
            'milestone_name' => $item['milestone']['name'] ?? 'Unknown Milestone',
            'project_name' => $item['project']['name'] ?? 'Unknown Project',
            'days' => $daysRemaining,
            'due_date' => isset($item['milestone']['due_date']) ? \Carbon\Carbon::parse($item['milestone']['due_date'])->format('Y-m-d') : 'Unknown Date',
            'completion_date' => isset($item['milestone']['completion_date']) ? \Carbon\Carbon::parse($item['milestone']['completion_date'])->format('Y-m-d') : 'Unknown Date'
        ];

        // Get platform notification messages
        $message = __("notifications.milestone_notifications.{$notificationType}", $translationParams, 'en');
        $messageAr = __("notifications.milestone_notifications.{$notificationType}", $translationParams, 'ar');

        foreach ($usersToNotify as $user) {
            // Send platform notification
            Notification::create([
                'user_id' => $user->user_id,
                'building_ids' => null,
                'message' => $message,
                'message_ar' => $messageAr,
                'section_type' => 'milestones',
                'section_id' => $item['project']['id'],
                'notification_sub_type' => "milestone_{$notificationType}",
                'created_at' => now(),
            ]);

            // Send email notification
            $this->sendEmailNotification($user, $milestoneData, $notificationType);
        }
    }

    private function sendEmailNotification($user, array $milestoneData, string $notificationType)
    {
        try {
            // Send English email
            if (!empty($user->email)) {
                Mail::to($user->email)->send(new MilestoneMail($milestoneData, $notificationType, 'en'));
            }

            // Send Arabic email if user has Arabic preference
            // You can add logic here to check user's language preference
            // For now, we'll send both languages to ensure coverage

        } catch (\Exception $e) {
            Log::error('Failed to send milestone email notification', [
                'user_id' => $user->user_id,
                'milestone' => $milestoneData['milestone']['name'] ?? 'Unknown',
                'type' => $notificationType,
                'error' => $e->getMessage()
            ]);
        }
    }
}
