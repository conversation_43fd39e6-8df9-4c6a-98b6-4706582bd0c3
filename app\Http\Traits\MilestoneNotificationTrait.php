<?php

namespace App\Http\Traits;

use App\Models\CrmUser;
use App\Models\Notification;
use App\Mail\MilestoneMail;
use App\Models\User;
use App\Services\CRM\Sales\ProjectService;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\App;

trait MilestoneNotificationTrait
{
    /**
     * Check and send milestone completion notifications after task status updates
     */
    protected function checkAndSendMilestoneCompletionNotifications($milestones, $projectId)
    {
        try {
            foreach ($milestones as $milestone) {
                if (isset($milestone['status']) && $milestone['status'] === 'complete') {
                    Log::info('MilestoneNotificationTrait: Found completed milestone', [
                        'milestone_id' => $milestone['id'] ?? 'unknown',
                        'milestone_title' => $milestone['title'] ?? 'unknown'
                    ]);

                    $this->sendMilestoneCompletionNotifications($milestone, $projectId);
                }
            }
        } catch (\Exception $e) {
            Log::error('MilestoneNotificationTrait: Error checking milestone completion notifications', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Send milestone completion notifications to project users
     */
    private function sendMilestoneCompletionNotifications($milestone, $projectId)
    {
        try {
            // Get project users from records
            $projectUsers = $this->records['allUsers'] ?? [];

            if (empty($projectUsers)) {
                Log::warning('MilestoneNotificationTrait: No project users found');
                return;
            }

            // Extract user IDs
            $userIds = array_column($projectUsers, 'id');

            logger()->debug('MilestoneNotificationTrait: User IDs for notification', [
                'user_ids' => $userIds
            ]);

            // Get actual user instances
            $usersToNotify = CrmUser::whereIn('crm_user_id', $userIds)->get();

            if ($usersToNotify->isEmpty()) {
                Log::warning('MilestoneNotificationTrait: No CRM users found for notification', [
                    'user_ids' => $userIds
                ]);
                return;
            }

            // Prepare milestone data for notifications
            $milestoneData = [
                'milestone_name' => $milestone['title'] ?? '',
                'project_name' => $this->records['project']['name'] ?? '',
                'completion_date' => date('Y-m-d'),
            ];

            Log::info('MilestoneNotificationTrait: Sending notifications to users', [
                'milestone_data' => $milestoneData,
                'user_count' => $usersToNotify->count()
            ]);

            // Send notifications to each user
            foreach ($usersToNotify as $user) {
                // Create platform notification
                Notification::create([
                    'user_id' => $user->user_id,
                    'message' => __('notifications.milestone_notifications.completed', $milestoneData, 'en'),
                    'message_ar' => __('notifications.milestone_notifications.completed', $milestoneData, 'ar'),
                    'section_type' => 'milestones',
                    'section_id' => $projectId,
                    'is_read' => 0,
                    'created_at' => now(),
                ]);

                // Send email notification in current application language only
                $currentLocale = $this->getCurrentApplicationLanguage();
                $this->sendEmailNotification($user, $milestoneData, $currentLocale);
            }

        } catch (\Exception $e) {
            Log::error('MilestoneNotificationTrait: Error sending milestone completion notifications', [
                'error' => $e->getMessage(),
                'milestone' => $milestone,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Send email notification for milestone completion
     */
    private function sendEmailNotification($user, $milestoneData, $locale)
    {
        try {
            // Get user email from CrmUser relationship or use a default method
            $userEmail = User::find($user->user_id)->email ?? null;

            if (!$userEmail) {
                Log::warning('MilestoneNotificationTrait: No email found for user', [
                    'user_id' => $user->user_id,
                    'crm_user_id' => $user->crm_user_id
                ]);
                return;
            }

            Mail::to($userEmail)->send(new MilestoneMail($milestoneData, 'completed', $locale));

            Log::info('MilestoneNotificationTrait: Email sent successfully', [
                'user_email' => $userEmail,
                'locale' => $locale,
                'milestone' => $milestoneData['milestone_name']
            ]);

        } catch (\Exception $e) {
            Log::error('MilestoneNotificationTrait: Error sending email notification', [
                'error' => $e->getMessage(),
                'user_id' => $user->user_id ?? 'unknown',
                'locale' => $locale,
                'trace' => $e->getTraceAsString()
            ]);
        }
    }

    /**
     * Get the current application language setting
     *
     * @return string
     */
    private function getCurrentApplicationLanguage()
    {
        try {
            // First, try to get the locale from session (user's current selection)
            // if (session()->has('locale')) {
            //     return session()->get('locale');
            // }

            // Fallback to current application locale
            return App::getLocale();
        } catch (\Exception $e) {
            Log::warning('MilestoneNotificationTrait: Error getting current language, defaulting to Arabic', [
                'error' => $e->getMessage()
            ]);

            // Default to Arabic as per application configuration
            return 'ar';
        }
    }
}
