{"__meta": {"id": "X3355580bfbb8a17a73682a382351ba03", "datetime": "2025-07-31 14:21:35", "utime": 1753960895.425368, "method": "POST", "uri": "/livewire/message/notifications.messages-notifications-list", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 3, "messages": [{"message": "[14:21:32] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753960892.8598, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:32] LOG.warning: Optional parameter $filePath declared before required parameter $message is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Services\\CRM\\CRMOmniChannelWhatsApp.php on line 120", "message_html": null, "is_string": false, "label": "warning", "time": 1753960892.981469, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:32] LOG.warning: Optional parameter $filename declared before required parameter $message is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Services\\CRM\\CRMOmniChannelWhatsApp.php on line 120", "message_html": null, "is_string": false, "label": "warning", "time": 1753960892.981527, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753960892.035738, "end": 1753960895.425399, "duration": 3.3896610736846924, "duration_str": "3.39s", "measures": [{"label": "Booting", "start": 1753960892.035738, "relative_start": 0, "end": 1753960892.815818, "relative_end": 1753960892.815818, "duration": 0.7800800800323486, "duration_str": "780ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753960892.815831, "relative_start": 0.7800929546356201, "end": 1753960895.4254, "relative_end": 9.5367431640625e-07, "duration": 2.6095690727233887, "duration_str": "2.61s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 38468176, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 1, "templates": [{"name": "livewire.notifications.messages-notifications-list (\\resources\\views\\livewire\\notifications\\messages-notifications-list.blade.php)", "param_count": 23, "params": ["chatList", "errors", "_instance", "workspaceSlug", "totalUnreadNotifications", "previousUnreadCount", "newList", "list", "slugs", "userId", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}]}, "route": {"uri": "POST livewire/message/{name}", "uses": "Livewire\\Controllers\\HttpConnectionHandler@__invoke", "controller": "Livewire\\Controllers\\HttpConnectionHandler", "as": "livewire.message", "middleware": "web"}, "queries": {"nb_statements": 6, "nb_failed_statements": 0, "accumulated_duration": 0.01064, "accumulated_duration_str": "10.64ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\AuthManager.php", "line": 340}, {"index": 20, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 23}], "duration": 0.00542, "duration_str": "5.42ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 50.94}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7368 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00135, "duration_str": "1.35ms", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_dev_db2", "start_percent": 50.94, "width_percent": 12.688}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\CRMOmniChannelWhatsApp.php", "line": 189}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\MessagesNotificationsList.php", "line": 40}], "duration": 0.00148, "duration_str": "1.48ms", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 63.628, "width_percent": 13.91}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\CRMOmniChannelWhatsApp.php", "line": 189}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\Notifications\\MessagesNotificationsList.php", "line": 40}], "duration": 0.0011200000000000001, "duration_str": "1.12ms", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 77.538, "width_percent": 10.526}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.0008, "duration_str": "800μs", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_dev_db2", "start_percent": 88.064, "width_percent": 7.519}, {"sql": "select * from `crm_user` where `crm_user`.`user_id` = 7368 and `crm_user`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "9740534860d5c11a9cf9e73e8939e4083f82ba1d", "line": 122}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 122}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "view::9740534860d5c11a9cf9e73e8939e4083f82ba1d:122", "connection": "osool_dev_db2", "start_percent": 95.583, "width_percent": 4.417}]}, "models": {"data": {"App\\Models\\CrmUser": 1, "App\\Models\\ProjectsDetails": 1, "App\\Models\\UserCompany": 1, "App\\Models\\User": 1}, "count": 4}, "livewire": {"data": {"notifications.messages-notifications-list #tN66SCIIIhHfz1avQbE9": "array:5 [\n  \"data\" => array:7 [\n    \"workspaceSlug\" => \"none\"\n    \"totalUnreadNotifications\" => 0\n    \"previousUnreadCount\" => 0\n    \"newList\" => null\n    \"list\" => []\n    \"slugs\" => array:3 [\n      0 => \"facebook\"\n      1 => \"whatsapp\"\n      2 => \"instagram\"\n    ]\n    \"userId\" => null\n  ]\n  \"name\" => \"notifications.messages-notifications-list\"\n  \"view\" => \"livewire.notifications.messages-notifications-list\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\MessagesNotificationsList\"\n  \"id\" => \"tN66SCIIIhHfz1avQbE9\"\n]"}, "count": 1}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL", "captcha_answer": "32", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "ar", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/livewire/message/notifications.messages-notifications-list", "status_code": "<pre class=sf-dump id=sf-dump-1628782611 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-1628782611\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "application/json", "request_query": "<pre class=sf-dump id=sf-dump-230816026 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-230816026\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1482477172 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>fingerprint</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"20 characters\">tN66SCIIIhHfz1avQbE9</span>\"\n    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"41 characters\">notifications.messages-notifications-list</span>\"\n    \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"220 characters\">CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n    \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n    \"<span class=sf-dump-key>v</span>\" => \"<span class=sf-dump-str title=\"3 characters\">acj</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>serverMemo</span>\" => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>children</span>\" => []\n    \"<span class=sf-dump-key>errors</span>\" => []\n    \"<span class=sf-dump-key>htmlHash</span>\" => \"<span class=sf-dump-str title=\"8 characters\">a6181f62</span>\"\n    \"<span class=sf-dump-key>data</span>\" => <span class=sf-dump-note>array:7</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>workspaceSlug</span>\" => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n      \"<span class=sf-dump-key>totalUnreadNotifications</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>previousUnreadCount</span>\" => <span class=sf-dump-num>0</span>\n      \"<span class=sf-dump-key>newList</span>\" => <span class=sf-dump-const>null</span>\n      \"<span class=sf-dump-key>list</span>\" => []\n      \"<span class=sf-dump-key>slugs</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">facebook</span>\"\n        <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">whatsapp</span>\"\n        <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"9 characters\">instagram</span>\"\n      </samp>]\n      \"<span class=sf-dump-key>userId</span>\" => <span class=sf-dump-const>null</span>\n    </samp>]\n    \"<span class=sf-dump-key>dataMeta</span>\" => []\n    \"<span class=sf-dump-key>checksum</span>\" => \"<span class=sf-dump-str title=\"64 characters\">a6d273ac58318811fbf69dddda77445e90eee94edf4a0f853c9c577e013c1f59</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>updates</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"10 characters\">callMethod</span>\"\n      \"<span class=sf-dump-key>payload</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>id</span>\" => \"<span class=sf-dump-str title=\"4 characters\">7yfm</span>\"\n        \"<span class=sf-dump-key>method</span>\" => \"<span class=sf-dump-str title=\"20 characters\">loadNotificationList</span>\"\n        \"<span class=sf-dump-key>params</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1482477172\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1264466576 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">790</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-livewire</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"242 characters\">http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Ijh5em9YV3dlMVFzWHZDRTArTmtzcFE9PSIsInZhbHVlIjoiY3dsVVZGd2FxN1QwRzZWalltdzhEbUpmb1lMVmVKZzJqUDIvYVRXM1g4QkZ2aHFremtoZDRDWDVVNTJvSnovVjRmZEVqRFdObDZTTFFqTTNkL0swZk44ZlI4Y2hvTUhuNmtLYVRkelhNL1JDVElOenByMmVqM0UzUjEzbm1qL04iLCJtYWMiOiI1ZjcyNjA1MTk2ZWEwZDliYzJiMDExMTQyNDQxODYzYTU1N2ZkMDAwMDc0MWVmMjYzODZhY2NmNjlkNzlmYzhlIiwidGFnIjoiIn0%3D; osool_session=uLMtamx6cUxP9tToRM7hteQOLTsuZfOzSyhIyZiL</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1264466576\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-689679814 data-indent-pad=\"  \"><span class=sf-dump-note>array:43</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>CONTENT_LENGTH</span>\" => \"<span class=sf-dump-str title=\"3 characters\">790</span>\"\n  \"<span class=sf-dump-key>HTTP_X_CSRF_TOKEN</span>\" => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"32 characters\">text/html, application/xhtml+xml</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>CONTENT_TYPE</span>\" => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  \"<span class=sf-dump-key>HTTP_X_LIVEWIRE</span>\" => \"<span class=sf-dump-str title=\"4 characters\">true</span>\"\n  \"<span class=sf-dump-key>HTTP_ORIGIN</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"242 characters\">http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6Ijh5em9YV3dlMVFzWHZDRTArTmtzcFE9PSIsInZhbHVlIjoiY3dsVVZGd2FxN1QwRzZWalltdzhEbUpmb1lMVmVKZzJqUDIvYVRXM1g4QkZ2aHFremtoZDRDWDVVNTJvSnovVjRmZEVqRFdObDZTTFFqTTNkL0swZk44ZlI4Y2hvTUhuNmtLYVRkelhNL1JDVElOenByMmVqM0UzUjEzbm1qL04iLCJtYWMiOiI1ZjcyNjA1MTk2ZWEwZDliYzJiMDExMTQyNDQxODYzYTU1N2ZkMDAwMDc0MWVmMjYzODZhY2NmNjlkNzlmYzhlIiwidGFnIjoiIn0%3D; osool_session=uLMtamx6cUxP9tToRM7hteQOLTsuZfOzSyhIyZiL</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">61094</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"59 characters\">/livewire/message/notifications.messages-notifications-list</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"4 characters\">POST</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"59 characters\">/livewire/message/notifications.messages-notifications-list</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753960892.0357</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753960892</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-689679814\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1802924300 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1802924300\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1154611266 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:21:35 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjNZT2xiLzhnNFJTR0Q2RHdxcXhsd2c9PSIsInZhbHVlIjoiUzZ2dW0wTlZyQXJrbENYQUlTQXZ3MWVCRDIwNEFrTHV1cU1COUpTZHhoV1pnU0kzUUF2Z2VKd1orRFFWMFZteHZVcVdPeEJHWGZoZmQ2QlB4bVZNb1JYUWxHM05nakYvakRUa1dYNUR5TFQwT1VKZWZra0FvUzNMZlFFQkFtcUIiLCJtYWMiOiIzMzJiOGE0Yjg0NjViODcwMTQzNjNmMmNhOTY2ZGQ0MGM0MjBmZWYwN2Y1ODFhMWZjMmU1N2MxNTViYjQ0MGQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:21:35 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6Ii8yTG5TQ3FsRkhGSEY5UUFFb05VbVE9PSIsInZhbHVlIjoiSUFZVmNhTkRNOTlkd0MydzBDUGdtN1Nyc05Zd0RqcTE1YnBWSWxQUG5ibnRGOEhIaHYxSDQ5NmFjU3kzNWo3cVhmVURqcVJYZU5xdXpJeFlya3RTU1ZuMnhqb2xUU2NIdG80TnZXdHJqUjdtWHgxUUZ1cHVyTFMweDg2d0luMWgiLCJtYWMiOiI5M2U3M2VmZjFjMWMzZDI5MjE1N2MxZWQ3NmVlYWIwM2JhZmI1ZTY1MWVmZWIyNDNiZTkyMTMxNzQyZWE3MDMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:21:35 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjNZT2xiLzhnNFJTR0Q2RHdxcXhsd2c9PSIsInZhbHVlIjoiUzZ2dW0wTlZyQXJrbENYQUlTQXZ3MWVCRDIwNEFrTHV1cU1COUpTZHhoV1pnU0kzUUF2Z2VKd1orRFFWMFZteHZVcVdPeEJHWGZoZmQ2QlB4bVZNb1JYUWxHM05nakYvakRUa1dYNUR5TFQwT1VKZWZra0FvUzNMZlFFQkFtcUIiLCJtYWMiOiIzMzJiOGE0Yjg0NjViODcwMTQzNjNmMmNhOTY2ZGQ0MGM0MjBmZWYwN2Y1ODFhMWZjMmU1N2MxNTViYjQ0MGQ2IiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:21:35 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6Ii8yTG5TQ3FsRkhGSEY5UUFFb05VbVE9PSIsInZhbHVlIjoiSUFZVmNhTkRNOTlkd0MydzBDUGdtN1Nyc05Zd0RqcTE1YnBWSWxQUG5ibnRGOEhIaHYxSDQ5NmFjU3kzNWo3cVhmVURqcVJYZU5xdXpJeFlya3RTU1ZuMnhqb2xUU2NIdG80TnZXdHJqUjdtWHgxUUZ1cHVyTFMweDg2d0luMWgiLCJtYWMiOiI5M2U3M2VmZjFjMWMzZDI5MjE1N2MxZWQ3NmVlYWIwM2JhZmI1ZTY1MWVmZWIyNDNiZTkyMTMxNzQyZWE3MDMwIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:21:35 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1154611266\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1180797524 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>32</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"242 characters\">http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1180797524\", {\"maxDepth\":0})</script>\n"}}