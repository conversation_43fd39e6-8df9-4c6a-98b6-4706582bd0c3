{"__meta": {"id": "X734fff294d8f34042be43e787fec1c7e", "datetime": "2025-07-31 14:21:31", "utime": 1753960891.023559, "method": "GET", "uri": "/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9", "ip": "127.0.0.1"}, "php": {"version": "8.3.16", "interface": "apache2handler"}, "messages": {"count": 11, "messages": [{"message": "[14:21:11] LOG.warning: Callables of the form [\"Swift_SmtpTransport\", \"Swift_Transport_EsmtpTransport::__construct\"] are deprecated in C:\\laragon\\www\\Osool-B2G\\vendor\\swiftmailer\\swiftmailer\\lib\\classes\\Swift\\SmtpTransport.php on line 36", "message_html": null, "is_string": false, "label": "warning", "time": 1753960871.623826, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:11] LOG.warning: Optional parameter $privilegeName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": 1753960871.719547, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:11] LOG.warning: Optional parameter $privilegeSectionName declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": 1753960871.719598, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:11] LOG.warning: Optional parameter $shouldBeTrue declared before required parameter $user is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 644", "message_html": null, "is_string": false, "label": "warning", "time": 1753960871.719638, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:30] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2416", "message_html": null, "is_string": false, "label": "warning", "time": **********.867659, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:30] LOG.warning: Optional parameter $privilege_name declared before required parameter $privilege_section_name is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 2476", "message_html": null, "is_string": false, "label": "warning", "time": **********.867763, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:30] LOG.warning: Optional parameter $search declared before required parameter $asset_id is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Helpers\\Helper.php on line 3617", "message_html": null, "is_string": false, "label": "warning", "time": **********.868353, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:30] LOG.warning: Optional parameter $filters declared before required parameter $userServiceProvider is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\WorkOrdersTrait.php on line 3414", "message_html": null, "is_string": false, "label": "warning", "time": **********.888925, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:30] LOG.warning: Optional parameter $serviceProviderId declared before required parameter $search is implicitly treated as a required parameter in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\UserTrait.php on line 328", "message_html": null, "is_string": false, "label": "warning", "time": **********.894329, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:30] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\app\\Http\\Traits\\FunctionsTrait.php on line 636", "message_html": null, "is_string": false, "label": "warning", "time": **********.933217, "xdebug_link": null, "collector": "log"}, {"message": "[14:21:30] LOG.warning: json_decode(): Passing null to parameter #1 ($json) of type string is deprecated in C:\\laragon\\www\\Osool-B2G\\storage\\framework\\views\\f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c.php on line 3", "message_html": null, "is_string": false, "label": "warning", "time": **********.965563, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1753960871.089982, "end": 1753960891.023604, "duration": 19.933621883392334, "duration_str": "19.93s", "measures": [{"label": "Booting", "start": 1753960871.089982, "relative_start": 0, "end": 1753960871.598331, "relative_end": 1753960871.598331, "duration": 0.5083489418029785, "duration_str": "508ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null}, {"label": "Application", "start": 1753960871.598344, "relative_start": 0.5083620548248291, "end": 1753960891.023607, "relative_end": 3.0994415283203125e-06, "duration": 19.425262928009033, "duration_str": "19.43s", "memory": 0, "memory_str": "0B", "params": [], "collector": null}]}, "memory": {"peak_usage": 47668160, "peak_usage_str": "45MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"nb_templates": 12, "templates": [{"name": "CRMProjects.project-details (\\resources\\views\\CRMProjects\\project-details.blade.php)", "param_count": 14, "params": ["projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.c-r-m-projects.empty-project (\\resources\\views\\livewire\\c-r-m-projects\\empty-project.blade.php)", "param_count": 81, "params": ["errors", "_instance", "projectID", "workspaceSlug", "projectData", "attachment", "chartData", "daysleft", "start_date", "filenametoDelete", "progress", "end_date", "budget", "description", "name", "input_name", "users", "project_type", "projectType", "priorityLevel", "priority_level", "projectExists", "isModalOpen", "attachments", "documentTypes", "projects", "DocumentsPaginated", "files", "isDeleteModalOpen", "filePaths", "<PERSON><PERSON><PERSON>", "maxFileSize", "attachmentFiles", "currentDate", "user", "settingsState", "milestoneData", "status", "statuss", "cost", "summary", "fileData", "type", "user_id", "subject", "notes", "project", "document_id", "projectDetails", "selectedUsersForInvite", "usersAlreadyInvited", "selectedvendorsForShare", "vendorsAlreadyInProject", "selectedclientsForShare", "clientssAlreadyInProject", "clients", "vendors", "fileType", "fileLanguage", "bmas_list", "assignedBMAList", "projectAccessUserId", "projectAccessDeleteMessage", "projectAccessUserName", "projectAccessUserType", "selectedBuildingsManager", "allBuildingsManager", "deleteAssignBMA", "currentPage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.app (\\resources\\views\\layouts\\app.blade.php)", "param_count": 18, "params": ["__env", "app", "errors", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._styles (\\resources\\views\\layouts\\partials\\_styles.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._header (\\resources\\views\\layouts\\partials\\_header.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "layouts.partials._top_menu (\\resources\\views\\layouts\\partials\\_top_menu.blade.php)", "param_count": 19, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html"], "type": "blade"}, {"name": "livewire.notifications.messages-notifications-list (\\resources\\views\\livewire\\notifications\\messages-notifications-list.blade.php)", "param_count": 23, "params": ["chatList", "errors", "_instance", "workspaceSlug", "totalUnreadNotifications", "previousUnreadCount", "newList", "list", "slugs", "userId", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.notifications.new-notifications-list-top-nav (\\resources\\views\\livewire\\notifications\\new-notifications-list-top-nav.blade.php)", "param_count": 28, "params": ["list", "totalUnreadNotifications", "errors", "_instance", "user", "perPage", "assignedAsset", "contractsIds", "accessBuildingsIds", "currentDate", "currentDateTime", "readyToLoad", "configOciLink", "ociLink", "selectedLanguage", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "livewire.menu.aside-nav-list (\\resources\\views\\livewire\\menu\\aside-nav-list.blade.php)", "param_count": 27, "params": ["userPrivilegesAside", "user", "hasViewPrivilege", "errors", "_instance", "has<PERSON>dmin", "projectId", "project", "workOrderMenuItemColor", "flagWorkorderSidebarMenu", "userPrivileges", "closedWorkOrderCount", "maintenanceRequestCount", "vendorRegistrationApplicationRequests", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount"], "type": "blade"}, {"name": "layouts.partials._footer (\\resources\\views\\layouts\\partials\\_footer.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials.check_crm_session (\\resources\\views\\layouts\\partials\\check_crm_session.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}, {"name": "layouts.partials._scripts (\\resources\\views\\layouts\\partials\\_scripts.blade.php)", "param_count": 21, "params": ["__env", "app", "errors", "_instance", "projectID", "workOrdersCount", "openWorkOrdersCount", "inprogressWorkOrdersCount", "sparePartsWorkOrdersCount", "underEvaluationWorkOrdersCount", "closedWorkOrdersCount", "bmWorkOrdersCount", "wfaWorkOrdersCount", "osoolAdminData", "pendingOfflineRequestCount", "check_use_erp_enabled", "projectDetails", "pendingComplaintsCount", "html", "url", "segments"], "type": "blade"}]}, "route": {"uri": "GET CRMProjects/details/{id}", "middleware": "web, auth", "controller": "App\\Http\\Controllers\\CRMProjects\\ProjectController@details", "as": "CRMProjects.details", "namespace": "App\\Http\\Controllers\\Admin", "prefix": "/CRMProjects", "where": [], "file": "<a href=\"phpstorm://open?file=C:\\laragon\\www\\Osool-B2G\\app\\Http\\Controllers\\CRMProjects\\ProjectController.php&line=19\">\\app\\Http\\Controllers\\CRMProjects\\ProjectController.php:19-32</a>"}, "queries": {"nb_statements": 48, "nb_failed_statements": 0, "accumulated_duration": 0.08768999999999999, "accumulated_duration_str": "87.69ms", "statements": [{"sql": "select * from `users` where `id` = 7368 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 53}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 60}, {"index": 18, "namespace": "middleware", "name": "auth", "line": 63}, {"index": 19, "namespace": "middleware", "name": "auth", "line": 42}], "duration": 0.0047599999999999995, "duration_str": "4.76ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php:53", "connection": "osool_dev_db2", "start_percent": 0, "width_percent": 5.428}, {"sql": "select * from `user_company` where `user_company`.`user_id` = 7368 and `user_company`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "\\app\\Services\\AkauntingService.php", "line": 148}, {"index": 21, "namespace": null, "name": "\\app\\Http\\Middleware\\AkauntingCompanyMiddleware.php", "line": 30}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}, {"index": 23, "namespace": null, "name": "\\app\\Http\\Middleware\\CheckSuperLogin.php", "line": 42}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 167}], "duration": 0.00093, "duration_str": "930μs", "stmt_id": "\\app\\Services\\AkauntingService.php:148", "connection": "osool_dev_db2", "start_percent": 5.428, "width_percent": 1.061}, {"sql": "select * from `projects_details` where `projects_details`.`id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "\\app\\Providers\\AsideViewComposerServiceProvider.php", "line": 59}, {"index": 22, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 162}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Concerns\\ManagesEvents.php", "line": 177}, {"index": 26, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 120}, {"index": 27, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 91}], "duration": 0.0020800000000000003, "duration_str": "2.08ms", "stmt_id": "\\app\\Providers\\AsideViewComposerServiceProvider.php:59", "connection": "osool_dev_db2", "start_percent": 6.489, "width_percent": 2.372}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 123}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 283}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 8.861, "width_percent": 0.924}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 123}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 283}], "duration": 0.00111, "duration_str": "1.11ms", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 9.784, "width_percent": 1.266}, {"sql": "update `users` set `workspace_slug` = '', `crm_api_token` = '', `users`.`modified_at` = '2025-07-31 14:21:13' where `id` = 7368", "type": "query", "params": [], "bindings": ["", "", "2025-07-31 14:21:13", "7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 112}, {"index": 14, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 15, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 123}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 283}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 150}], "duration": 0.00218, "duration_str": "2.18ms", "stmt_id": "\\app\\Services\\DashCrmService.php:112", "connection": "osool_dev_db2", "start_percent": 11.05, "width_percent": 2.486}, {"sql": "insert into `audits` (`old_values`, `new_values`, `event`, `auditable_id`, `auditable_type`, `user_id`, `user_type`, `tags`, `ip_address`, `user_agent`, `url`, `updated_at`, `created_at`) values ('{\\\"workspace_slug\\\":\\\"khansaa-test34444\\\",\\\"crm_api_token\\\":\\\"eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzOTU2MTg2LCJleHAiOjE3NTM5NTk3ODYsIm5iZiI6MTc1Mzk1NjE4NiwianRpIjoiSW43VjhDVWMwczhhcUZzUyIsInN1YiI6IjY1IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.eKuTx5DPPQTR2j_8lDmEcbcOkcvOgoRWz6q4bbO6pvs\\\"}', '{\\\"workspace_slug\\\":null,\\\"crm_api_token\\\":null}', 'updated', 7368, 'App\\Models\\User', 7368, 'App\\Models\\User', '', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (<PERSON>HT<PERSON>, like Gecko) Chrome/********* Safari/537.36', 'http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9', '2025-07-31 14:21:13', '2025-07-31 14:21:13')", "type": "query", "params": [], "bindings": ["{&quot;workspace_slug&quot;:&quot;khansaa-test34444&quot;,&quot;crm_api_token&quot;:&quot;eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiJodHRwczovL3dvcmtkby1kZXYub3Nvb2wuY2xvdWQvYXBpL2xvZ2luIiwiaWF0IjoxNzUzOTU2MTg2LCJleHAiOjE3NTM5NTk3ODYsIm5iZiI6MTc1Mzk1NjE4NiwianRpIjoiSW43VjhDVWMwczhhcUZzUyIsInN1YiI6IjY1IiwicHJ2IjoiMjNiZDVjODk0OWY2MDBhZGIzOWU3MDFjNDAwODcyZGI3YTU5NzZmNyJ9.eKuTx5DPPQTR2j_8lDmEcbcOkcvOgoRWz6q4bbO6pvs&quot;}", "{&quot;workspace_slug&quot;:null,&quot;crm_api_token&quot;:null}", "updated", "7368", "App\\Models\\User", "7368", "App\\Models\\User", "", "127.0.0.1", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9", "2025-07-31 14:21:13", "2025-07-31 14:21:13"], "hints": null, "show_copy": false, "backtrace": [{"index": 23, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php", "line": 83}, {"index": 25, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 99}, {"index": 26, "namespace": null, "name": "\\vendor\\owen-it\\laravel-auditing\\src\\AuditableObserver.php", "line": 49}, {"index": 32, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 112}, {"index": 33, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}], "duration": 0.00279, "duration_str": "2.79ms", "stmt_id": "\\vendor\\owen-it\\laravel-auditing\\src\\Auditor.php:83", "connection": "osool_dev_db2", "start_percent": 13.536, "width_percent": 3.182}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', 1753960873, 1753960873, '{\\\"uuid\\\":\\\"06ceb1e5-0483-428f-a903-823b67949af2\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"command\\\":\\\"O:24:\\\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\\\":12:{s:8:\\\\\"\\u0000*\\u0000email\\\\\";s:24:\\\\\"<EMAIL>\\\\\";s:11:\\\\\"\\u0000*\\u0000password\\\\\";s:6:\\\\\"123456\\\\\";s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "1753960873", "1753960873", "{&quot;uuid&quot;:&quot;06ceb1e5-0483-428f-a903-823b67949af2&quot;,&quot;displayName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;failOnTimeout&quot;:false,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;command&quot;:&quot;O:24:\\&quot;App\\\\Jobs\\\\ProcessCrmLogin\\&quot;:12:{s:8:\\&quot;\\u0000*\\u0000email\\&quot;;s:24:\\&quot;<EMAIL>\\&quot;;s:11:\\&quot;\\u0000*\\u0000password\\&quot;;s:6:\\&quot;123456\\&quot;;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:11:\\&quot;afterCommit\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}], "duration": 0.00199, "duration_str": "1.99ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:181", "connection": "osool_dev_db2", "start_percent": 16.718, "width_percent": 2.269}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 127}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 530}], "duration": 0.00098, "duration_str": "980μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 18.987, "width_percent": 1.118}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 127}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 530}], "duration": 0.0007199999999999999, "duration_str": "720μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 20.105, "width_percent": 0.821}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', 1753960875, 1753960875, '{\\\"uuid\\\":\\\"a0b91f10-f698-464e-992e-85387ebe2bd1\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"command\\\":\\\"O:24:\\\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\\\":12:{s:8:\\\\\"\\u0000*\\u0000email\\\\\";s:24:\\\\\"<EMAIL>\\\\\";s:11:\\\\\"\\u0000*\\u0000password\\\\\";s:6:\\\\\"123456\\\\\";s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "1753960875", "1753960875", "{&quot;uuid&quot;:&quot;a0b91f10-f698-464e-992e-85387ebe2bd1&quot;,&quot;displayName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;failOnTimeout&quot;:false,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;command&quot;:&quot;O:24:\\&quot;App\\\\Jobs\\\\ProcessCrmLogin\\&quot;:12:{s:8:\\&quot;\\u0000*\\u0000email\\&quot;;s:24:\\&quot;khan<PERSON><PERSON><EMAIL>\\&quot;;s:11:\\&quot;\\u0000*\\u0000password\\&quot;;s:6:\\&quot;123456\\&quot;;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:11:\\&quot;afterCommit\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}], "duration": 0.00307, "duration_str": "3.07ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:181", "connection": "osool_dev_db2", "start_percent": 20.926, "width_percent": 3.501}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 131}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 540}], "duration": 0.00128, "duration_str": "1.28ms", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 24.427, "width_percent": 1.46}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 131}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 540}], "duration": 0.00092, "duration_str": "920μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 25.887, "width_percent": 1.049}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', 1753960877, 1753960877, '{\\\"uuid\\\":\\\"7cda8c34-1349-4cb5-9fe1-528383ee5f10\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"command\\\":\\\"O:24:\\\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\\\":12:{s:8:\\\\\"\\u0000*\\u0000email\\\\\";s:24:\\\\\"<EMAIL>\\\\\";s:11:\\\\\"\\u0000*\\u0000password\\\\\";s:6:\\\\\"123456\\\\\";s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "1753960877", "1753960877", "{&quot;uuid&quot;:&quot;7cda8c34-1349-4cb5-9fe1-528383ee5f10&quot;,&quot;displayName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;failOnTimeout&quot;:false,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;command&quot;:&quot;O:24:\\&quot;App\\\\Jobs\\\\ProcessCrmLogin\\&quot;:12:{s:8:\\&quot;\\u0000*\\u0000email\\&quot;;s:24:\\&quot;<EMAIL>\\&quot;;s:11:\\&quot;\\u0000*\\u0000password\\&quot;;s:6:\\&quot;123456\\&quot;;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:11:\\&quot;afterCommit\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}], "duration": 0.004019999999999999, "duration_str": "4.02ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:181", "connection": "osool_dev_db2", "start_percent": 26.936, "width_percent": 4.584}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 328}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 549}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 31.52, "width_percent": 0.844}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 328}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 549}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 32.364, "width_percent": 0.924}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', 1753960879, 1753960879, '{\\\"uuid\\\":\\\"3c73db8c-8f08-4839-84dc-e3ffda691c4b\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"command\\\":\\\"O:24:\\\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\\\":12:{s:8:\\\\\"\\u0000*\\u0000email\\\\\";s:24:\\\\\"<EMAIL>\\\\\";s:11:\\\\\"\\u0000*\\u0000password\\\\\";s:6:\\\\\"123456\\\\\";s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "1753960879", "1753960879", "{&quot;uuid&quot;:&quot;3c73db8c-8f08-4839-84dc-e3ffda691c4b&quot;,&quot;displayName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;failOnTimeout&quot;:false,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;command&quot;:&quot;O:24:\\&quot;App\\\\Jobs\\\\ProcessCrmLogin\\&quot;:12:{s:8:\\&quot;\\u0000*\\u0000email\\&quot;;s:24:\\&quot;khan<PERSON><EMAIL>\\&quot;;s:11:\\&quot;\\u0000*\\u0000password\\&quot;;s:6:\\&quot;123456\\&quot;;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:11:\\&quot;afterCommit\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}], "duration": 0.0053, "duration_str": "5.3ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:181", "connection": "osool_dev_db2", "start_percent": 33.288, "width_percent": 6.044}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 349}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 193}], "duration": 0.00123, "duration_str": "1.23ms", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 39.332, "width_percent": 1.403}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 349}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 193}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 40.734, "width_percent": 0.684}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', 1753960881, 1753960881, '{\\\"uuid\\\":\\\"9428f5a6-f5d0-4916-b8a8-fbc2e48bd603\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"command\\\":\\\"O:24:\\\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\\\":12:{s:8:\\\\\"\\u0000*\\u0000email\\\\\";s:24:\\\\\"<EMAIL>\\\\\";s:11:\\\\\"\\u0000*\\u0000password\\\\\";s:6:\\\\\"123456\\\\\";s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "1753960881", "1753960881", "{&quot;uuid&quot;:&quot;9428f5a6-f5d0-4916-b8a8-fbc2e48bd603&quot;,&quot;displayName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;failOnTimeout&quot;:false,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;command&quot;:&quot;O:24:\\&quot;App\\\\Jobs\\\\ProcessCrmLogin\\&quot;:12:{s:8:\\&quot;\\u0000*\\u0000email\\&quot;;s:24:\\&quot;khan<PERSON><PERSON><EMAIL>\\&quot;;s:11:\\&quot;\\u0000*\\u0000password\\&quot;;s:6:\\&quot;123456\\&quot;;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:11:\\&quot;afterCommit\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}], "duration": 0.00228, "duration_str": "2.28ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:181", "connection": "osool_dev_db2", "start_percent": 41.419, "width_percent": 2.6}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 354}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 202}], "duration": 0.00098, "duration_str": "980μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 44.019, "width_percent": 1.118}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 354}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 202}], "duration": 0.00065, "duration_str": "650μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 45.136, "width_percent": 0.741}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', 1753960883, 1753960883, '{\\\"uuid\\\":\\\"5dc4f6fe-a479-4d87-8aff-0d18a7d42873\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"command\\\":\\\"O:24:\\\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\\\":12:{s:8:\\\\\"\\u0000*\\u0000email\\\\\";s:24:\\\\\"<EMAIL>\\\\\";s:11:\\\\\"\\u0000*\\u0000password\\\\\";s:6:\\\\\"123456\\\\\";s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "1753960883", "1753960883", "{&quot;uuid&quot;:&quot;5dc4f6fe-a479-4d87-8aff-0d18a7d42873&quot;,&quot;displayName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;failOnTimeout&quot;:false,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;command&quot;:&quot;O:24:\\&quot;App\\\\Jobs\\\\ProcessCrmLogin\\&quot;:12:{s:8:\\&quot;\\u0000*\\u0000email\\&quot;;s:24:\\&quot;khan<PERSON><EMAIL>\\&quot;;s:11:\\&quot;\\u0000*\\u0000password\\&quot;;s:6:\\&quot;123456\\&quot;;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:11:\\&quot;afterCommit\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}], "duration": 0.0038900000000000002, "duration_str": "3.89ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:181", "connection": "osool_dev_db2", "start_percent": 45.878, "width_percent": 4.436}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 56}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 231}], "duration": 0.00159, "duration_str": "1.59ms", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 50.314, "width_percent": 1.813}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 56}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 231}], "duration": 0.00074, "duration_str": "740μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 52.127, "width_percent": 0.844}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', 1753960885, 1753960885, '{\\\"uuid\\\":\\\"72bf5294-964a-461b-9f49-72aff0c86e42\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"command\\\":\\\"O:24:\\\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\\\":12:{s:8:\\\\\"\\u0000*\\u0000email\\\\\";s:24:\\\\\"<EMAIL>\\\\\";s:11:\\\\\"\\u0000*\\u0000password\\\\\";s:6:\\\\\"123456\\\\\";s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "1753960885", "1753960885", "{&quot;uuid&quot;:&quot;72bf5294-964a-461b-9f49-72aff0c86e42&quot;,&quot;displayName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;failOnTimeout&quot;:false,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;command&quot;:&quot;O:24:\\&quot;App\\\\Jobs\\\\ProcessCrmLogin\\&quot;:12:{s:8:\\&quot;\\u0000*\\u0000email\\&quot;;s:24:\\&quot;<EMAIL>\\&quot;;s:11:\\&quot;\\u0000*\\u0000password\\&quot;;s:6:\\&quot;123456\\&quot;;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:11:\\&quot;afterCommit\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}], "duration": 0.0028, "duration_str": "2.8ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:181", "connection": "osool_dev_db2", "start_percent": 52.971, "width_percent": 3.193}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 254}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 598}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 56.164, "width_percent": 0.924}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 254}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 598}], "duration": 0.00047, "duration_str": "470μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 57.087, "width_percent": 0.536}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', 1753960887, 1753960887, '{\\\"uuid\\\":\\\"4e94d5a5-7b7d-406e-9e46-fcafddbb7006\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"command\\\":\\\"O:24:\\\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\\\":12:{s:8:\\\\\"\\u0000*\\u0000email\\\\\";s:24:\\\\\"<EMAIL>\\\\\";s:11:\\\\\"\\u0000*\\u0000password\\\\\";s:6:\\\\\"123456\\\\\";s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "1753960887", "1753960887", "{&quot;uuid&quot;:&quot;4e94d5a5-7b7d-406e-9e46-fcafddbb7006&quot;,&quot;displayName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;failOnTimeout&quot;:false,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;command&quot;:&quot;O:24:\\&quot;App\\\\Jobs\\\\ProcessCrmLogin\\&quot;:12:{s:8:\\&quot;\\u0000*\\u0000email\\&quot;;s:24:\\&quot;khan<PERSON><PERSON><EMAIL>\\&quot;;s:11:\\&quot;\\u0000*\\u0000password\\&quot;;s:6:\\&quot;123456\\&quot;;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:11:\\&quot;afterCommit\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}], "duration": 0.00229, "duration_str": "2.29ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:181", "connection": "osool_dev_db2", "start_percent": 57.623, "width_percent": 2.611}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 278}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 609}], "duration": 0.0008100000000000001, "duration_str": "810μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 60.235, "width_percent": 0.924}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 278}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 609}], "duration": 0.00095, "duration_str": "950μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 61.159, "width_percent": 1.083}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', 1753960889, 1753960889, '{\\\"uuid\\\":\\\"0835fb38-5d42-415a-a889-3f9438ad161f\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"command\\\":\\\"O:24:\\\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\\\":12:{s:8:\\\\\"\\u0000*\\u0000email\\\\\";s:24:\\\\\"<EMAIL>\\\\\";s:11:\\\\\"\\u0000*\\u0000password\\\\\";s:6:\\\\\"123456\\\\\";s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "1753960889", "1753960889", "{&quot;uuid&quot;:&quot;0835fb38-5d42-415a-a889-3f9438ad161f&quot;,&quot;displayName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;failOnTimeout&quot;:false,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;command&quot;:&quot;O:24:\\&quot;App\\\\Jobs\\\\ProcessCrmLogin\\&quot;:12:{s:8:\\&quot;\\u0000*\\u0000email\\&quot;;s:24:\\&quot;<EMAIL>\\&quot;;s:11:\\&quot;\\u0000*\\u0000password\\&quot;;s:6:\\&quot;123456\\&quot;;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:11:\\&quot;afterCommit\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}], "duration": 0.0066, "duration_str": "6.6ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:181", "connection": "osool_dev_db2", "start_percent": 62.242, "width_percent": 7.527}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 339}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 1473}], "duration": 0.0007099999999999999, "duration_str": "710μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 69.769, "width_percent": 0.81}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\app\\Services\\DashCrmService.php", "line": 56}, {"index": 18, "namespace": null, "name": "\\app\\Services\\CRM\\Projects\\ProjectServices.php", "line": 339}, {"index": 19, "namespace": null, "name": "\\app\\Http\\Livewire\\CRMProjects\\ProjectDetails.php", "line": 1473}], "duration": 0.00043, "duration_str": "430μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 70.578, "width_percent": 0.49}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, '', **********, **********, '{\\\"uuid\\\":\\\"a74c57d1-478f-4f10-8616-a62488291257\\\",\\\"displayName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"job\\\":\\\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\",\\\"command\\\":\\\"O:24:\\\\\"App\\\\Jobs\\\\ProcessCrmLogin\\\\\":12:{s:8:\\\\\"\\u0000*\\u0000email\\\\\";s:24:\\\\\"<EMAIL>\\\\\";s:11:\\\\\"\\u0000*\\u0000password\\\\\";s:6:\\\\\"123456\\\\\";s:3:\\\\\"job\\\\\";N;s:10:\\\\\"connection\\\\\";N;s:5:\\\\\"queue\\\\\";N;s:15:\\\\\"chainConnection\\\\\";N;s:10:\\\\\"chainQueue\\\\\";N;s:19:\\\\\"chainCatchCallbacks\\\\\";N;s:5:\\\\\"delay\\\\\";N;s:11:\\\\\"afterCommit\\\\\";N;s:10:\\\\\"middleware\\\\\";a:0:{}s:7:\\\\\"chained\\\\\";a:0:{}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", "0", "", "**********", "**********", "{&quot;uuid&quot;:&quot;a74c57d1-478f-4f10-8616-a62488291257&quot;,&quot;displayName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;job&quot;:&quot;Illuminate\\\\Queue\\\\CallQueuedHandler@call&quot;,&quot;maxTries&quot;:null,&quot;maxExceptions&quot;:null,&quot;failOnTimeout&quot;:false,&quot;backoff&quot;:null,&quot;timeout&quot;:null,&quot;retryUntil&quot;:null,&quot;data&quot;:{&quot;commandName&quot;:&quot;App\\\\Jobs\\\\ProcessCrmLogin&quot;,&quot;command&quot;:&quot;O:24:\\&quot;App\\\\Jobs\\\\ProcessCrmLogin\\&quot;:12:{s:8:\\&quot;\\u0000*\\u0000email\\&quot;;s:24:\\&quot;<EMAIL>\\&quot;;s:11:\\&quot;\\u0000*\\u0000password\\&quot;;s:6:\\&quot;123456\\&quot;;s:3:\\&quot;job\\&quot;;N;s:10:\\&quot;connection\\&quot;;N;s:5:\\&quot;queue\\&quot;;N;s:15:\\&quot;chainConnection\\&quot;;N;s:10:\\&quot;chainQueue\\&quot;;N;s:19:\\&quot;chainCatchCallbacks\\&quot;;N;s:5:\\&quot;delay\\&quot;;N;s:11:\\&quot;afterCommit\\&quot;;N;s:10:\\&quot;middleware\\&quot;;a:0:{}s:7:\\&quot;chained\\&quot;;a:0:{}}&quot;}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 181}, {"index": 12, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 317}, {"index": 14, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 253}], "duration": 0.00232, "duration_str": "2.32ms", "stmt_id": "\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php:181", "connection": "osool_dev_db2", "start_percent": 71.069, "width_percent": 2.646}, {"sql": "select * from `release_notes` where `store_status` = 1 and `release_notes`.`deleted_at` is null group by `version`", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 3046}, {"index": 15, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 51}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00279, "duration_str": "2.79ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:3046", "connection": "osool_dev_db2", "start_percent": 73.714, "width_percent": 3.182}, {"sql": "select * from `crm_user` where `crm_user`.`user_id` = 7368 and `crm_user`.`user_id` is not null limit 1", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": "view", "name": "9740534860d5c11a9cf9e73e8939e4083f82ba1d", "line": 122}, {"index": 22, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 23, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 24, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}, {"index": 25, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 122}], "duration": 0.00057, "duration_str": "570μs", "stmt_id": "view::9740534860d5c11a9cf9e73e8939e4083f82ba1d:122", "connection": "osool_dev_db2", "start_percent": 76.896, "width_percent": 0.65}, {"sql": "select `name`, `name_ar` from `user_type` where `slug` = 'admin' limit 1", "type": "query", "params": [], "bindings": ["admin"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 1927}, {"index": 14, "namespace": "view", "name": "8798481a8e56dccb941dae0e544cebc8cb0bca4d", "line": 161}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0015300000000000001, "duration_str": "1.53ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:1927", "connection": "osool_dev_db2", "start_percent": 77.546, "width_percent": 1.745}, {"sql": "select `id`, `project_image`, `use_beneficiary_module`, `use_tenant_module`, `benificiary_status`, `tenant_status`, `project_name`, `project_name_ar`, `use_crm_module` from `projects_details` where `id` = 201 and `projects_details`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\ProjectDetailTrait.php", "line": 11}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 128}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 71}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0006, "duration_str": "600μs", "stmt_id": "\\app\\Http\\Traits\\ProjectDetailTrait.php:11", "connection": "osool_dev_db2", "start_percent": 79.291, "width_percent": 0.684}, {"sql": "select * from `work_orders` where `project_user_id` = 7368", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 14, "namespace": null, "name": "\\app\\Services\\RequestedItemService.php", "line": 21}, {"index": 15, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 148}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 73}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00227, "duration_str": "2.27ms", "stmt_id": "\\app\\Services\\RequestedItemService.php:21", "connection": "osool_dev_db2", "start_percent": 79.975, "width_percent": 2.589}, {"sql": "select count(*) as aggregate from `service_provider_missing_items_requests` where `status` = 'requested' and 0 = 1", "type": "query", "params": [], "bindings": ["requested"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Services\\RequestedItemService.php", "line": 26}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 148}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 73}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.0005899999999999999, "duration_str": "590μs", "stmt_id": "\\app\\Services\\RequestedItemService.php:26", "connection": "osool_dev_db2", "start_percent": 82.564, "width_percent": 0.673}, {"sql": "select `id`, `created_at` from `users` where `project_id` = 201 and `user_type` = 'admin' and `status` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["201", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 256}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Traits\\UserTrait.php", "line": 267}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 158}, {"index": 18, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 74}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}], "duration": 0.00344, "duration_str": "3.44ms", "stmt_id": "\\app\\Http\\Traits\\UserTrait.php:256", "connection": "osool_dev_db2", "start_percent": 83.236, "width_percent": 3.923}, {"sql": "select count(*) as aggregate from `work_orders` inner join `contracts` on `contracts`.`id` = `work_orders`.`contract_id` inner join `property_buildings` on `property_buildings`.`id` = `work_orders`.`property_id` inner join `users` on `users`.`id` = `work_orders`.`created_by` where `work_orders`.`status` = 4 and `work_orders`.`is_deleted` = 'no' and `work_orders`.`start_date` <= '2025-07-31' and `work_orders`.`project_user_id` = 7368", "type": "query", "params": [], "bindings": ["4", "no", "2025-07-31", "7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "\\app\\Http\\Traits\\WorkOrdersTrait.php", "line": 2102}, {"index": 16, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 191}, {"index": 17, "namespace": null, "name": "\\app\\Http\\Livewire\\Menu\\AsideNavList.php", "line": 78}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php", "line": 36}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php", "line": 40}], "duration": 0.00579, "duration_str": "5.79ms", "stmt_id": "\\app\\Http\\Traits\\WorkOrdersTrait.php:2102", "connection": "osool_dev_db2", "start_percent": 87.159, "width_percent": 6.603}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 892}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00063, "duration_str": "630μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 93.762, "width_percent": 0.718}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "77a1a8487800179e2f9f8576eb7a3805ef086207", "line": 892}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 84}, {"index": 19, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 59}, {"index": 20, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php", "line": 139}], "duration": 0.00038, "duration_str": "380μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 94.481, "width_percent": 0.433}, {"sql": "select `id` from `users` where `project_id` = 201 and `user_type` = 'admin' and `status` = 1 limit 1", "type": "query", "params": [], "bindings": ["201", "admin", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 13, "namespace": null, "name": "\\app\\Http\\Helpers\\Helper.php", "line": 928}, {"index": 14, "namespace": "view", "name": "f8d18d6f63e9bf36e98d3bb1b82c9e7d86b4c71c", "line": 13}, {"index": 16, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 17, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 18, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.0035099999999999997, "duration_str": "3.51ms", "stmt_id": "\\app\\Http\\Helpers\\Helper.php:928", "connection": "osool_dev_db2", "start_percent": 94.914, "width_percent": 4.003}, {"sql": "select exists(select * from `projects_details` where `id` = 201 and `use_crm_module` = 1 and `projects_details`.`deleted_at` is null) as `exists`", "type": "query", "params": [], "bindings": ["201", "1"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00058, "duration_str": "580μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 98.917, "width_percent": 0.661}, {"sql": "select exists(select * from `crm_user` where `user_id` = 7368) as `exists`", "type": "query", "params": [], "bindings": ["7368"], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "\\app\\Models\\User.php", "line": 750}, {"index": 16, "namespace": "view", "name": "2c36a85657ec19d9aee4fefa5309928007e1bdb1", "line": 183}, {"index": 18, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php", "line": 108}, {"index": 19, "namespace": null, "name": "\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php", "line": 58}, {"index": 20, "namespace": null, "name": "\\vendor\\livewire\\livewire\\src\\ComponentConcerns\\RendersLivewireComponents.php", "line": 69}], "duration": 0.00037, "duration_str": "370μs", "stmt_id": "\\app\\Models\\User.php:750", "connection": "osool_dev_db2", "start_percent": 99.578, "width_percent": 0.422}]}, "models": {"data": {"App\\Models\\CrmUser": 1, "App\\Models\\ReleaseNotes": 1, "App\\Models\\ProjectsDetails": 2, "App\\Models\\UserCompany": 1, "App\\Models\\User": 2}, "count": 7}, "livewire": {"data": {"c-r-m-projects.project-details #9vZlYbaLBPMHZGoPe7Xf": "array:5 [\n  \"data\" => array:67 [\n    \"projectID\" => 635\n    \"workspaceSlug\" => \"khansaa-test34444\"\n    \"projectData\" => []\n    \"attachment\" => null\n    \"chartData\" => []\n    \"daysleft\" => \"\"\n    \"start_date\" => null\n    \"filenametoDelete\" => null\n    \"progress\" => 0\n    \"end_date\" => null\n    \"budget\" => null\n    \"description\" => null\n    \"name\" => null\n    \"input_name\" => null\n    \"users\" => []\n    \"project_type\" => null\n    \"projectType\" => []\n    \"priorityLevel\" => []\n    \"priority_level\" => null\n    \"projectExists\" => false\n    \"isModalOpen\" => false\n    \"attachments\" => []\n    \"documentTypes\" => []\n    \"projects\" => []\n    \"DocumentsPaginated\" => []\n    \"files\" => []\n    \"isDeleteModalOpen\" => false\n    \"filePaths\" => []\n    \"folderKey\" => \"uploads\"\n    \"maxFileSize\" => 5120\n    \"attachmentFiles\" => null\n    \"currentDate\" => null\n    \"user\" => null\n    \"settingsState\" => array:19 [\n      \"basic_details\" => 0\n      \"member\" => 0\n      \"client\" => 0\n      \"vendor\" => 0\n      \"milestone\" => 0\n      \"activity\" => 0\n      \"attachment\" => 0\n      \"task\" => 0\n      \"bug_report\" => 0\n      \"invoice\" => 0\n      \"bill\" => 0\n      \"timesheet\" => 0\n      \"documents\" => 0\n      \"progress\" => 0\n      \"password_protected\" => 0\n      \"password\" => 0\n      \"retainer\" => 0\n      \"proposal\" => 0\n      \"procurement\" => 0\n    ]\n    \"milestoneData\" => []\n    \"status\" => \"incomplete\"\n    \"statuss\" => null\n    \"cost\" => null\n    \"summary\" => null\n    \"fileData\" => []\n    \"type\" => null\n    \"user_id\" => null\n    \"subject\" => null\n    \"notes\" => null\n    \"project\" => null\n    \"document_id\" => null\n    \"projectDetails\" => null\n    \"selectedUsersForInvite\" => []\n    \"usersAlreadyInvited\" => []\n    \"selectedvendorsForShare\" => []\n    \"vendorsAlreadyInProject\" => []\n    \"selectedclientsForShare\" => []\n    \"clientssAlreadyInProject\" => []\n    \"clients\" => []\n    \"vendors\" => []\n    \"fileType\" => 1\n    \"fileLanguage\" => 1\n    \"bmas_list\" => []\n    \"assignedBMAList\" => []\n    \"projectAccessUserId\" => 0\n    \"projectAccessDeleteMessage\" => \"\"\n    \"projectAccessUserName\" => \"\"\n    \"projectAccessUserType\" => \"\"\n    \"selectedBuildingsManager\" => []\n    \"allBuildingsManager\" => []\n    \"deleteAssignBMA\" => array:2 [\n      \"bma_id\" => \"\"\n      \"bma_name\" => \"\"\n    ]\n    \"currentPage\" => []\n  ]\n  \"name\" => \"c-r-m-projects.project-details\"\n  \"view\" => \"livewire.c-r-m-projects.empty-project\"\n  \"component\" => \"App\\Http\\Livewire\\CRMProjects\\ProjectDetails\"\n  \"id\" => \"9vZlYbaLBPMHZGoPe7Xf\"\n]", "notifications.messages-notifications-list #tN66SCIIIhHfz1avQbE9": "array:5 [\n  \"data\" => array:7 [\n    \"workspaceSlug\" => \"none\"\n    \"totalUnreadNotifications\" => 0\n    \"previousUnreadCount\" => 0\n    \"newList\" => null\n    \"list\" => []\n    \"slugs\" => array:3 [\n      0 => \"facebook\"\n      1 => \"whatsapp\"\n      2 => \"instagram\"\n    ]\n    \"userId\" => null\n  ]\n  \"name\" => \"notifications.messages-notifications-list\"\n  \"view\" => \"livewire.notifications.messages-notifications-list\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\MessagesNotificationsList\"\n  \"id\" => \"tN66SCIIIhHfz1avQbE9\"\n]", "notifications.new-notifications-list-top-nav #xcHOfOJ9DXei1AqpDZNL": "array:5 [\n  \"data\" => array:11 [\n    \"user\" => null\n    \"perPage\" => null\n    \"assignedAsset\" => null\n    \"contractsIds\" => null\n    \"accessBuildingsIds\" => null\n    \"currentDate\" => null\n    \"currentDateTime\" => null\n    \"readyToLoad\" => null\n    \"configOciLink\" => null\n    \"ociLink\" => null\n    \"selectedLanguage\" => null\n  ]\n  \"name\" => \"notifications.new-notifications-list-top-nav\"\n  \"view\" => \"livewire.notifications.new-notifications-list-top-nav\"\n  \"component\" => \"App\\Http\\Livewire\\Notifications\\NewNotificationsListTopNav\"\n  \"id\" => \"xcHOfOJ9DXei1AqpDZNL\"\n]", "menu.aside-nav-list #": "array:7 [\n  \"data\" => array:10 [\n    \"user\" => App\\Models\\User {#3968\n      #connection: \"mysql\"\n      #table: \"users\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:69 [\n        \"id\" => 7368\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$Nrgb2fubI18xCSRqaKjUjOzVJbptwn80b0U.ID9srqgW/v2G.7cpy\"\n        \"name\" => \"Crm Test 02\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => \"99090\"\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 201\n        \"project_user_id\" => 7368\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-26 20:35:15\"\n        \"modified_at\" => \"2025-07-31 14:21:13\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => null\n        \"crm_api_token\" => null\n      ]\n      #original: array:69 [\n        \"id\" => 7368\n        \"email\" => \"<EMAIL>\"\n        \"password\" => \"$2y$10$Nrgb2fubI18xCSRqaKjUjOzVJbptwn80b0U.ID9srqgW/v2G.7cpy\"\n        \"name\" => \"Crm Test 02\"\n        \"first_name\" => null\n        \"last_name\" => null\n        \"phone\" => null\n        \"profile_img\" => null\n        \"emp_id\" => \"99090\"\n        \"profession_id\" => null\n        \"emp_dept\" => null\n        \"building_ids\" => null\n        \"contract_ids\" => null\n        \"supervisor_id\" => null\n        \"sp_admin_id\" => null\n        \"address\" => null\n        \"country_id\" => 1\n        \"city_id\" => 1\n        \"role_regions\" => null\n        \"role_cities\" => null\n        \"asset_categories\" => null\n        \"keeper_warehouses\" => null\n        \"properties\" => null\n        \"contracts\" => null\n        \"beneficiary\" => null\n        \"service_provider\" => \"1\"\n        \"user_type\" => \"admin\"\n        \"user_privileges\" => null\n        \"approved_max_amount\" => null\n        \"created_by\" => 21\n        \"project_id\" => 201\n        \"project_user_id\" => 7368\n        \"device_token\" => null\n        \"device_type\" => \"android\"\n        \"api_token\" => null\n        \"otp\" => null\n        \"apartment\" => null\n        \"unit_receival_date\" => null\n        \"unit_receival_later_clicked_at\" => null\n        \"langForSms\" => \"Arabic\"\n        \"otp_verified\" => 0\n        \"email_verified\" => 0\n        \"email_attempts\" => 0\n        \"last_email_attempt_at\" => null\n        \"allow_akaunting\" => 1\n        \"status\" => 1\n        \"is_deleted\" => \"no\"\n        \"created_at\" => \"2025-02-26 20:35:15\"\n        \"modified_at\" => \"2025-07-31 14:21:13\"\n        \"save_later_date\" => null\n        \"favorite_language\" => \"-\"\n        \"last_ip\" => null\n        \"deleted_at\" => null\n        \"last_login_datetime\" => null\n        \"temp_password\" => null\n        \"otp_for_password\" => null\n        \"otp_for_password_verified\" => 0\n        \"selected_app_langugage\" => \"en\"\n        \"temp_phone_number\" => null\n        \"is_subcontractors_worker\" => 0\n        \"first_login\" => 1\n        \"is_unit_link\" => 0\n        \"later_booking_alert\" => null\n        \"akaunting_vendor_id\" => null\n        \"akaunting_customer_id\" => null\n        \"associated_workdo_id\" => null\n        \"is_bma_area_manager\" => 0\n        \"workspace_slug\" => null\n        \"crm_api_token\" => null\n      ]\n      #changes: array:3 [\n        \"modified_at\" => \"2025-07-31 14:21:13\"\n        \"workspace_slug\" => null\n        \"crm_api_token\" => null\n      ]\n      #casts: array:2 [\n        \"email_verified_at\" => \"datetime\"\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: array:3 [\n        \"userCompany\" => App\\Models\\UserCompany {#3975\n          #connection: \"mysql\"\n          #table: \"user_company\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:5 [\n            \"id\" => 360\n            \"user_id\" => 7368\n            \"company_id\" => 264\n            \"created_at\" => \"2025-02-26 20:35:21\"\n            \"updated_at\" => \"2025-02-26 20:35:21\"\n          ]\n          #original: array:5 [\n            \"id\" => 360\n            \"user_id\" => 7368\n            \"company_id\" => 264\n            \"created_at\" => \"2025-02-26 20:35:21\"\n            \"updated_at\" => \"2025-02-26 20:35:21\"\n          ]\n          #changes: []\n          #casts: array:2 [\n            \"user_id\" => \"int\"\n            \"company_id\" => \"int\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: array:2 [\n            0 => \"created_at\"\n            1 => \"updated_at\"\n          ]\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"company_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n        \"projectDetails\" => App\\Models\\ProjectsDetails {#3994\n          #connection: \"mysql\"\n          #table: \"projects_details\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:22 [\n            \"id\" => 201\n            \"user_id\" => 0\n            \"project_name\" => \"Khadeer CRM Test 02\"\n            \"project_name_ar\" => \"Khadeer CRM Test 02\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-26 17:34:01\"\n            \"updated_at\" => \"2025-04-23 13:56:58\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n          ]\n          #original: array:22 [\n            \"id\" => 201\n            \"user_id\" => 0\n            \"project_name\" => \"Khadeer CRM Test 02\"\n            \"project_name_ar\" => \"Khadeer CRM Test 02\"\n            \"industry_type\" => \"1\"\n            \"project_image\" => null\n            \"created_by\" => 0\n            \"is_deleted\" => 0\n            \"created_at\" => \"2025-02-26 17:34:01\"\n            \"updated_at\" => \"2025-04-23 13:56:58\"\n            \"use_erp_module\" => 1\n            \"use_crm_module\" => 1\n            \"use_tenant_module\" => 1\n            \"tenant_status\" => 1\n            \"use_beneficiary_module\" => 1\n            \"benificiary_status\" => 1\n            \"community_status\" => 1\n            \"contract_status\" => 1\n            \"contract_start_date\" => null\n            \"contract_end_date\" => null\n            \"share_post\" => 1\n            \"deleted_at\" => null\n          ]\n          #changes: []\n          #casts: array:1 [\n            \"deleted_at\" => \"datetime\"\n          ]\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:16 [\n            0 => \"user_id\"\n            1 => \"project_name\"\n            2 => \"project_name_ar\"\n            3 => \"project_image\"\n            4 => \"industry_type\"\n            5 => \"created_by\"\n            6 => \"is_deleted\"\n            7 => \"use_erp_module\"\n            8 => \"use_tenant_module\"\n            9 => \"tenant_status\"\n            10 => \"use_beneficiary_module\"\n            11 => \"benificiary_status\"\n            12 => \"community_status\"\n            13 => \"contract_status\"\n            14 => \"share_post\"\n            15 => \"use_crm_module\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n          #forceDeleting: false\n          #excludedAttributes: []\n          +auditEvent: null\n          +auditCustomOld: null\n          +auditCustomNew: null\n          +isCustomEvent: false\n          +preloadedResolverData: []\n        }\n        \"crmUser\" => App\\Models\\CrmUser {#4288\n          #connection: \"mysql\"\n          #table: \"crm_user\"\n          #primaryKey: \"id\"\n          #keyType: \"int\"\n          +incrementing: true\n          #with: []\n          #withCount: []\n          +preventsLazyLoading: false\n          #perPage: 15\n          +exists: true\n          +wasRecentlyCreated: false\n          #escapeWhenCastingToString: false\n          #attributes: array:8 [\n            \"id\" => 2\n            \"user_id\" => 7368\n            \"crm_user_id\" => 65\n            \"created_at\" => \"2025-02-26 20:35:15\"\n            \"updated_at\" => \"2025-02-26 20:35:15\"\n            \"instagram_connect\" => 1\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n          ]\n          #original: array:8 [\n            \"id\" => 2\n            \"user_id\" => 7368\n            \"crm_user_id\" => 65\n            \"created_at\" => \"2025-02-26 20:35:15\"\n            \"updated_at\" => \"2025-02-26 20:35:15\"\n            \"instagram_connect\" => 1\n            \"facebook_connect\" => 0\n            \"whatsapp_connect\" => 0\n          ]\n          #changes: []\n          #casts: []\n          #classCastCache: []\n          #attributeCastCache: []\n          #dates: []\n          #dateFormat: null\n          #appends: []\n          #dispatchesEvents: []\n          #observables: []\n          #relations: []\n          #touches: []\n          +timestamps: true\n          #hidden: []\n          #visible: []\n          #fillable: array:2 [\n            0 => \"user_id\"\n            1 => \"crm_user_id\"\n          ]\n          #guarded: array:1 [\n            0 => \"*\"\n          ]\n        }\n      ]\n      #touches: []\n      +timestamps: true\n      #hidden: array:2 [\n        0 => \"password\"\n        1 => \"remember_token\"\n      ]\n      #visible: []\n      #fillable: array:54 [\n        0 => \"allow_akaunting\"\n        1 => \"email\"\n        2 => \"password\"\n        3 => \"name\"\n        4 => \"first_name\"\n        5 => \"last_name\"\n        6 => \"apartment\"\n        7 => \"unit_receival_date\"\n        8 => \"later_booking_alert\"\n        9 => \"phone\"\n        10 => \"profile_img\"\n        11 => \"address\"\n        12 => \"country_id\"\n        13 => \"city_id\"\n        14 => \"role_regions\"\n        15 => \"role_cities\"\n        16 => \"asset_categories\"\n        17 => \"properties\"\n        18 => \"contracts\"\n        19 => \"beneficiary\"\n        20 => \"service_provider\"\n        21 => \"user_type\"\n        22 => \"project_id\"\n        23 => \"project_user_id\"\n        24 => \"created_by\"\n        25 => \"status\"\n        26 => \"user_privileges\"\n        27 => \"approved_max_amount\"\n        28 => \"emp_id\"\n        29 => \"profession_id\"\n        30 => \"emp_dept\"\n        31 => \"building_ids\"\n        32 => \"contract_ids\"\n        33 => \"supervisor_id\"\n        34 => \"sp_admin_id\"\n        35 => \"langForSms\"\n        36 => \"deleted_at\"\n        37 => \"otp\"\n        38 => \"temp_password\"\n        39 => \"otp_for_password\"\n        40 => \"otp_for_password_verified\"\n        41 => \"temp_phone_number\"\n        42 => \"favorite_language\"\n        43 => \"is_subcontractors_worker\"\n        44 => \"keeper_warehouses\"\n        45 => \"save_later_date\"\n        46 => \"first_login\"\n        47 => \"is_unit_link\"\n        48 => \"akaunting_vendor_id\"\n        49 => \"akaunting_customer_id\"\n        50 => \"crm_api_token\"\n        51 => \"workspace_slug\"\n        52 => \"is_bma_area_manager\"\n        53 => \"assigned_workers\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #rememberTokenName: \"remember_token\"\n      #accessToken: null\n      #forceDeleting: false\n      #excludedAttributes: array:3 [\n        0 => \"created_at\"\n        1 => \"modified_at\"\n        2 => \"deleted_at\"\n      ]\n      +auditEvent: \"updated\"\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: array:4 [\n        \"ip_address\" => \"127.0.0.1\"\n        \"user_agent\" => \"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36\"\n        \"url\" => \"http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9\"\n        \"user\" => App\\Models\\User {#3968}\n      ]\n      -roleClass: null\n      -permissionClass: null\n      -wildcardClass: null\n    }\n    \"hasAdmin\" => 7368\n    \"projectId\" => null\n    \"project\" => App\\Models\\ProjectsDetails {#4305\n      #connection: \"mysql\"\n      #table: \"projects_details\"\n      #primaryKey: \"id\"\n      #keyType: \"int\"\n      +incrementing: true\n      #with: []\n      #withCount: []\n      +preventsLazyLoading: false\n      #perPage: 15\n      +exists: true\n      +wasRecentlyCreated: false\n      #escapeWhenCastingToString: false\n      #attributes: array:9 [\n        \"id\" => 201\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khadeer CRM Test 02\"\n        \"project_name_ar\" => \"Khadeer CRM Test 02\"\n        \"use_crm_module\" => 1\n      ]\n      #original: array:9 [\n        \"id\" => 201\n        \"project_image\" => null\n        \"use_beneficiary_module\" => 1\n        \"use_tenant_module\" => 1\n        \"benificiary_status\" => 1\n        \"tenant_status\" => 1\n        \"project_name\" => \"Khadeer CRM Test 02\"\n        \"project_name_ar\" => \"Khadeer CRM Test 02\"\n        \"use_crm_module\" => 1\n      ]\n      #changes: []\n      #casts: array:1 [\n        \"deleted_at\" => \"datetime\"\n      ]\n      #classCastCache: []\n      #attributeCastCache: []\n      #dates: []\n      #dateFormat: null\n      #appends: []\n      #dispatchesEvents: []\n      #observables: []\n      #relations: []\n      #touches: []\n      +timestamps: true\n      #hidden: []\n      #visible: []\n      #fillable: array:16 [\n        0 => \"user_id\"\n        1 => \"project_name\"\n        2 => \"project_name_ar\"\n        3 => \"project_image\"\n        4 => \"industry_type\"\n        5 => \"created_by\"\n        6 => \"is_deleted\"\n        7 => \"use_erp_module\"\n        8 => \"use_tenant_module\"\n        9 => \"tenant_status\"\n        10 => \"use_beneficiary_module\"\n        11 => \"benificiary_status\"\n        12 => \"community_status\"\n        13 => \"contract_status\"\n        14 => \"share_post\"\n        15 => \"use_crm_module\"\n      ]\n      #guarded: array:1 [\n        0 => \"*\"\n      ]\n      #forceDeleting: false\n      #excludedAttributes: []\n      +auditEvent: null\n      +auditCustomOld: null\n      +auditCustomNew: null\n      +isCustomEvent: false\n      +preloadedResolverData: []\n    }\n    \"workOrderMenuItemColor\" => \"#000\"\n    \"flagWorkorderSidebarMenu\" => false\n    \"userPrivileges\" => null\n    \"closedWorkOrderCount\" => 0\n    \"maintenanceRequestCount\" => 0\n    \"vendorRegistrationApplicationRequests\" => null\n  ]\n  \"oldData\" => null\n  \"actionQueue\" => null\n  \"name\" => \"menu.aside-nav-list\"\n  \"view\" => \"livewire.menu.aside-nav-list\"\n  \"component\" => \"App\\Http\\Livewire\\Menu\\AsideNavList\"\n  \"id\" => null\n]"}, "count": 4}, "swiftmailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL", "captcha_answer": "32", "_previous": "array:1 [\n  \"url\" => \"http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "7368", "plain_user_password": "123456", "locale": "ar", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"path_info": "/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9", "status_code": "<pre class=sf-dump id=sf-dump-616079776 data-indent-pad=\"  \"><span class=sf-dump-num>200</span>\n</pre><script>Sfdump(\"sf-dump-616079776\", {\"maxDepth\":0})</script>\n", "status_text": "OK", "format": "html", "content_type": "text/html; charset=UTF-8", "request_query": "<pre class=sf-dump id=sf-dump-818522480 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-818522480\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-763155036 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-763155036\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1789629375 data-indent-pad=\"  \"><span class=sf-dump-note>array:11</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/CRMProjects/List?page=1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InFCZHpSNnRaVHBNblFOcm5jbHNnb0E9PSIsInZhbHVlIjoib2NobWk1OWZhMElKWkk3OFpweDRTMUxZQllDSTBzZC9RL085VngyL2tLZ2pKaE1HbDUwdExmNHBsSnBFMUNCU05LODArQVVtS3JDR05Ici9lRkQ1MlF5YmptcTFFbE1lNlRHdm5mK0FHR0hrUy9PRENrNTJ0MkF0WUg2eVdGYm0iLCJtYWMiOiI4NDYwNmZjM2MxZjYxMzU4NTk3Y2QyYmM5YzU4MTlkYjQyMjU4Y2JjODkwZjY2NmIwODViMzFkNjQxNjFhYjcyIiwidGFnIjoiIn0%3D; osool_session=YXLztnA3ht6jaBJH0cCIXTRP8tPt7ADp7Crsk71Y</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1789629375\", {\"maxDepth\":0})</script>\n", "request_server": "<pre class=sf-dump id=sf-dump-1568140072 data-indent-pad=\"  \"><span class=sf-dump-note>array:40</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>REDIRECT_STATUS</span>\" => \"<span class=sf-dump-str title=\"3 characters\">200</span>\"\n  \"<span class=sf-dump-key>HTTP_HOST</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>HTTP_CONNECTION</span>\" => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  \"<span class=sf-dump-key>HTTP_CACHE_CONTROL</span>\" => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  \"<span class=sf-dump-key>HTTP_DNT</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_UPGRADE_INSECURE_REQUESTS</span>\" => \"<span class=sf-dump-str>1</span>\"\n  \"<span class=sf-dump-key>HTTP_USER_AGENT</span>\" => \"<span class=sf-dump-str title=\"111 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT</span>\" => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  \"<span class=sf-dump-key>HTTP_REFERER</span>\" => \"<span class=sf-dump-str title=\"45 characters\">http://osool-b2g.test/CRMProjects/List?page=1</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_ENCODING</span>\" => \"<span class=sf-dump-str title=\"13 characters\">gzip, deflate</span>\"\n  \"<span class=sf-dump-key>HTTP_ACCEPT_LANGUAGE</span>\" => \"<span class=sf-dump-str title=\"95 characters\">en-US,en;q=0.9,ar;q=0.8,bg;q=0.7,es;q=0.6,fr;q=0.5,he;q=0.4,ms;q=0.3,pt;q=0.2,it;q=0.1,pl;q=0.1</span>\"\n  \"<span class=sf-dump-key>HTTP_COOKIE</span>\" => \"<span class=sf-dump-str title=\"409 characters\">XSRF-TOKEN=eyJpdiI6InFCZHpSNnRaVHBNblFOcm5jbHNnb0E9PSIsInZhbHVlIjoib2NobWk1OWZhMElKWkk3OFpweDRTMUxZQllDSTBzZC9RL085VngyL2tLZ2pKaE1HbDUwdExmNHBsSnBFMUNCU05LODArQVVtS3JDR05Ici9lRkQ1MlF5YmptcTFFbE1lNlRHdm5mK0FHR0hrUy9PRENrNTJ0MkF0WUg2eVdGYm0iLCJtYWMiOiI4NDYwNmZjM2MxZjYxMzU4NTk3Y2QyYmM5YzU4MTlkYjQyMjU4Y2JjODkwZjY2NmIwODViMzFkNjQxNjFhYjcyIiwidGFnIjoiIn0%3D; osool_session=YXLztnA3ht6jaBJH0cCIXTRP8tPt7ADp7Crsk71Y</span>\"\n  \"<span class=sf-dump-key>PATH</span>\" => \"<span class=sf-dump-str title=\"1017 characters\">C:\\Program Files\\Parallels\\Parallels Tools\\Applications;C:\\WINDOWS\\system32;C:\\WINDOWS;C:\\WINDOWS\\System32\\Wbem;C:\\WINDOWS\\System32\\WindowsPowerShell\\v1.0\\;C:\\WINDOWS\\System32\\OpenSSH\\;C:\\Program Files\\Git\\cmd;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\ProgramData\\ComposerSetup\\bin;C:\\laragon\\bin\\composer;C:\\laragon\\bin\\git\\bin;C:\\laragon\\bin\\git\\cmd;C:\\laragon\\bin\\git\\mingw64\\bin;C:\\laragon\\bin\\git\\usr\\bin;C:\\laragon\\bin\\mysql\\mysql-8.4.3-winx64\\bin;C:\\laragon\\bin\\ngrok;C:\\laragon\\bin\\nodejs\\node-v22;C:\\laragon\\bin\\php\\php-8.3.16-Win32-vs16-x64;C:\\laragon\\bin\\python\\python-3.13;C:\\laragon\\bin\\python\\python-3.13\\Scripts;C:\\laragon\\usr\\bin;C:\\Users\\<USER>\\AppData\\Local\\Yarn\\config\\global\\node_modules\\.bin;C:\\Users\\<USER>\\AppData\\Roaming\\Composer\\vendor\\bin;C:\\Users\\<USER>\\AppData\\Roaming\\npm;C:\\Users\\<USER>\\AppData\\Local\\Microsoft\\WindowsApps;C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\Microsoft VS Code\\bin;C:\\Users\\<USER>\\AppData\\Local\\Programs\\cursor\\resources\\app\\bin</span>\"\n  \"<span class=sf-dump-key>SystemRoot</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>COMSPEC</span>\" => \"<span class=sf-dump-str title=\"27 characters\">C:\\WINDOWS\\system32\\cmd.exe</span>\"\n  \"<span class=sf-dump-key>PATHEXT</span>\" => \"<span class=sf-dump-str title=\"53 characters\">.COM;.EXE;.BAT;.CMD;.VBS;.VBE;.JS;.JSE;.WSF;.WSH;.MSC</span>\"\n  \"<span class=sf-dump-key>WINDIR</span>\" => \"<span class=sf-dump-str title=\"10 characters\">C:\\WINDOWS</span>\"\n  \"<span class=sf-dump-key>SERVER_SIGNATURE</span>\" => \"\"\n  \"<span class=sf-dump-key>SERVER_SOFTWARE</span>\" => \"<span class=sf-dump-str title=\"47 characters\">Apache/2.4.62 (Win64) OpenSSL/3.0.15 PHP/8.3.16</span>\"\n  \"<span class=sf-dump-key>SERVER_NAME</span>\" => \"<span class=sf-dump-str title=\"14 characters\">osool-b2g.test</span>\"\n  \"<span class=sf-dump-key>SERVER_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PORT</span>\" => \"<span class=sf-dump-str title=\"2 characters\">80</span>\"\n  \"<span class=sf-dump-key>REMOTE_ADDR</span>\" => \"<span class=sf-dump-str title=\"9 characters\">127.0.0.1</span>\"\n  \"<span class=sf-dump-key>DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>REQUEST_SCHEME</span>\" => \"<span class=sf-dump-str title=\"4 characters\">http</span>\"\n  \"<span class=sf-dump-key>CONTEXT_PREFIX</span>\" => \"\"\n  \"<span class=sf-dump-key>CONTEXT_DOCUMENT_ROOT</span>\" => \"<span class=sf-dump-str title=\"31 characters\">C:/laragon/www/Osool-B2G/public</span>\"\n  \"<span class=sf-dump-key>SERVER_ADMIN</span>\" => \"<span class=sf-dump-str title=\"17 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>SCRIPT_FILENAME</span>\" => \"<span class=sf-dump-str title=\"41 characters\">C:/laragon/www/Osool-B2G/public/index.php</span>\"\n  \"<span class=sf-dump-key>REMOTE_PORT</span>\" => \"<span class=sf-dump-str title=\"5 characters\">60969</span>\"\n  \"<span class=sf-dump-key>REDIRECT_URL</span>\" => \"<span class=sf-dump-str title=\"221 characters\">/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>GATEWAY_INTERFACE</span>\" => \"<span class=sf-dump-str title=\"7 characters\">CGI/1.1</span>\"\n  \"<span class=sf-dump-key>SERVER_PROTOCOL</span>\" => \"<span class=sf-dump-str title=\"8 characters\">HTTP/1.1</span>\"\n  \"<span class=sf-dump-key>REQUEST_METHOD</span>\" => \"<span class=sf-dump-str title=\"3 characters\">GET</span>\"\n  \"<span class=sf-dump-key>QUERY_STRING</span>\" => \"\"\n  \"<span class=sf-dump-key>REQUEST_URI</span>\" => \"<span class=sf-dump-str title=\"221 characters\">/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n  \"<span class=sf-dump-key>SCRIPT_NAME</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>PHP_SELF</span>\" => \"<span class=sf-dump-str title=\"10 characters\">/index.php</span>\"\n  \"<span class=sf-dump-key>REQUEST_TIME_FLOAT</span>\" => <span class=sf-dump-num>1753960871.09</span>\n  \"<span class=sf-dump-key>REQUEST_TIME</span>\" => <span class=sf-dump-num>1753960871</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1568140072\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1504564652 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>osool_session</span>\" => <span class=sf-dump-const>null</span>\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1504564652\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1395449339 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"55 characters\">max-age=0, must-revalidate, no-cache, no-store, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Thu, 31 Jul 2025 11:21:11 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>pragma</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">no-cache</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>expires</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Fri, 01 Jan 1990 00:00:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>set-cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"428 characters\">XSRF-TOKEN=eyJpdiI6IjJWNzV4RUF2dDFtZkdyeTdkdnZ3MUE9PSIsInZhbHVlIjoicUdIa3dXUERNbEljWXAyYWhSczFjUUYvWVlRS3RHbFBpSlpIaUVUQmkzWTZvZFMrZWtoN3ZkekJiWm5ZQjZBS25DbzdyaG5Va3ZWeldLRkJZbGs0WDRPRUVzYkdUK24vSmNkSWZMU1g0eGdDNWtmb3FvTWVzR1NUMEI3alpDcnciLCJtYWMiOiJmOTQyYjM4ZGY4NWE4ZmQ1MjliZWIzOGM3YTdlZDIwODJmMjMwN2ZjMDkyZmFkOWFjMjI4NTYzMGU2NDgxYTkxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:21:31 GMT; Max-Age=7200; path=/; samesite=lax</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"441 characters\">osool_session=eyJpdiI6ImVVb1ppUThZMU5XRWEwbk1sNFVSWXc9PSIsInZhbHVlIjoiVFpyZlV3Z1ZkQ2Vrb2ZQR29zRE9QMlowTlhWWmo1bTdwYTZYa1lWSEkyVUlCeDh6SEovaDVzTURsU0EwY0wzU2FGSGwrcHlnblhzQkNIZ29XdHROc0dSbjBtN1BzS3pqUTFiVkcxNjU1NnJwN2kxNXR0TUlpdHdMWlNJUXovVWsiLCJtYWMiOiJiY2UzZjU3MmU4N2MzNjIyMTI2MjlkNTc4OGViMDdjMDZmNjdkYzc3Yzg1MDFlNzM5MWM0NmJhMGViMGQ0ZmRmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:21:31 GMT; Max-Age=7200; path=/; httponly; samesite=lax</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>Set-Cookie</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"400 characters\">XSRF-TOKEN=eyJpdiI6IjJWNzV4RUF2dDFtZkdyeTdkdnZ3MUE9PSIsInZhbHVlIjoicUdIa3dXUERNbEljWXAyYWhSczFjUUYvWVlRS3RHbFBpSlpIaUVUQmkzWTZvZFMrZWtoN3ZkekJiWm5ZQjZBS25DbzdyaG5Va3ZWeldLRkJZbGs0WDRPRUVzYkdUK24vSmNkSWZMU1g0eGdDNWtmb3FvTWVzR1NUMEI3alpDcnciLCJtYWMiOiJmOTQyYjM4ZGY4NWE4ZmQ1MjliZWIzOGM3YTdlZDIwODJmMjMwN2ZjMDkyZmFkOWFjMjI4NTYzMGU2NDgxYTkxIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:21:31 GMT; path=/</span>\"\n    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"413 characters\">osool_session=eyJpdiI6ImVVb1ppUThZMU5XRWEwbk1sNFVSWXc9PSIsInZhbHVlIjoiVFpyZlV3Z1ZkQ2Vrb2ZQR29zRE9QMlowTlhWWmo1bTdwYTZYa1lWSEkyVUlCeDh6SEovaDVzTURsU0EwY0wzU2FGSGwrcHlnblhzQkNIZ29XdHROc0dSbjBtN1BzS3pqUTFiVkcxNjU1NnJwN2kxNXR0TUlpdHdMWlNJUXovVWsiLCJtYWMiOiJiY2UzZjU3MmU4N2MzNjIyMTI2MjlkNTc4OGViMDdjMDZmNjdkYzc3Yzg1MDFlNzM5MWM0NmJhMGViMGQ0ZmRmIiwidGFnIjoiIn0%3D; expires=Thu, 31-Jul-2025 13:21:31 GMT; path=/; httponly</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1395449339\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-824159267 data-indent-pad=\"  \"><span class=sf-dump-note>array:8</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">82Y3S2Ii8XAEUfJqJwoJcxv4amqMyZ5Eyd3DAmvL</span>\"\n  \"<span class=sf-dump-key>captcha_answer</span>\" => <span class=sf-dump-num>32</span>\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"242 characters\">http://osool-b2g.test/CRMProjects/details/eyJpdiI6IlQwdWlvem15NUdvYWlTYnhEK09aaWc9PSIsInZhbHVlIjoiaHJWaHowdnpKNWkySk1ISkpNS2pTQT09IiwibWFjIjoiOWNhMzIyYjM2Nzc4MTJiY2U4NWE2ZTg3MTMxNWQzMTZhM2FhZWZjZWYxZThjMjhmNTg1NjVlZTYyZjVmYWIzOCIsInRhZyI6IiJ9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>7368</span>\n  \"<span class=sf-dump-key>plain_user_password</span>\" => \"<span class=sf-dump-str title=\"6 characters\">123456</span>\"\n  \"<span class=sf-dump-key>locale</span>\" => \"<span class=sf-dump-str title=\"2 characters\">ar</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-824159267\", {\"maxDepth\":0})</script>\n"}}